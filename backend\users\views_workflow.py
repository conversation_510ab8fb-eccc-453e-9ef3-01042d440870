"""
Workflow Management Views

API endpoints for managing workflow switching and group assignments.
"""

from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from django.db import transaction
from django_tenants.utils import schema_context
from tenants.models import Tenant
from tenants.models_workflow import TenantWorkflowConfig
from users.services.workflow_group_manager import WorkflowGroupManager
from users.serializers_workflow import (
    WorkflowSwitchSerializer, WorkflowStatusSerializer, 
    DesignatedPrinterSerializer, WorkflowImpactSerializer
)
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class WorkflowManagementViewSet(viewsets.ViewSet):
    """
    API endpoints for workflow management and group switching.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def has_permission(self, request, view):
        """Check if user can manage workflows."""
        user = request.user
        
        # Superusers can manage all workflows
        if user.is_superuser:
            return True
        
        # Subcity admins and subcity system admins can manage kebele workflows in their subcity
        if user.role in ['subcity_admin', 'subcity_system_admin']:
            return True
        
        # Kebele leaders can manage their own kebele workflow (autonomous only)
        if user.role == 'kebele_leader' and hasattr(user, 'tenant'):
            return user.tenant.type == 'kebele'
        
        return False
    
    @action(detail=False, methods=['get'])
    def workflow_status(self, request):
        """
        Get current workflow status for a tenant.
        """
        tenant_id = request.query_params.get('tenant_id')
        if not tenant_id:
            return Response(
                {'error': 'tenant_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            tenant = Tenant.objects.get(id=tenant_id)
        except Tenant.DoesNotExist:
            return Response(
                {'error': 'Tenant not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Check permission for this tenant
        if not self._can_manage_tenant_workflow(request.user, tenant):
            return Response(
                {'error': 'Permission denied for this tenant'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        workflow_manager = WorkflowGroupManager(tenant)
        status_data = workflow_manager.get_current_workflow_status()
        
        serializer = WorkflowStatusSerializer(status_data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def switch_workflow(self, request):
        """
        Switch tenant workflow between centralized and autonomous.
        """
        serializer = WorkflowSwitchSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        tenant_id = serializer.validated_data['tenant_id']
        new_workflow = serializer.validated_data['workflow_type']
        reason = serializer.validated_data.get('reason', '')
        
        try:
            tenant = Tenant.objects.get(id=tenant_id)
        except Tenant.DoesNotExist:
            return Response(
                {'error': 'Tenant not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Check permission for this tenant
        if not self._can_manage_tenant_workflow(request.user, tenant):
            return Response(
                {'error': 'Permission denied for this tenant'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Additional validation for autonomous workflow
        if new_workflow == 'autonomous' and tenant.type != 'kebele':
            return Response(
                {'error': 'Autonomous workflow is only available for kebele tenants'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            workflow_manager = WorkflowGroupManager(tenant)
            result = workflow_manager.switch_workflow(
                new_workflow_type=new_workflow,
                switched_by=request.user,
                reason=reason
            )
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error switching workflow: {e}")
            return Response(
                {'error': f'Failed to switch workflow: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['post'])
    def assign_designated_printer(self, request):
        """
        Assign designated printer role to a user (autonomous workflow only).
        """
        serializer = DesignatedPrinterSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        tenant_id = serializer.validated_data['tenant_id']
        user_email = serializer.validated_data['user_email']
        
        try:
            tenant = Tenant.objects.get(id=tenant_id)
        except Tenant.DoesNotExist:
            return Response(
                {'error': 'Tenant not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Check permission for this tenant
        if not self._can_manage_tenant_workflow(request.user, tenant):
            return Response(
                {'error': 'Permission denied for this tenant'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        try:
            workflow_manager = WorkflowGroupManager(tenant)
            success = workflow_manager.assign_designated_printer(user_email)
            
            if success:
                return Response({
                    'success': True,
                    'message': f'Successfully assigned {user_email} as designated printer',
                    'user_email': user_email,
                    'tenant': tenant.name
                })
            else:
                return Response(
                    {'error': 'Failed to assign designated printer'},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Error assigning designated printer: {e}")
            return Response(
                {'error': f'Failed to assign designated printer: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def workflow_impact(self, request):
        """
        Analyze the impact of switching to a different workflow.
        """
        tenant_id = request.query_params.get('tenant_id')
        target_workflow = request.query_params.get('target_workflow')
        
        if not tenant_id or not target_workflow:
            return Response(
                {'error': 'tenant_id and target_workflow parameters are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            tenant = Tenant.objects.get(id=tenant_id)
        except Tenant.DoesNotExist:
            return Response(
                {'error': 'Tenant not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Check permission for this tenant
        if not self._can_manage_tenant_workflow(request.user, tenant):
            return Response(
                {'error': 'Permission denied for this tenant'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Analyze impact
        impact_data = self._analyze_workflow_impact(tenant, target_workflow)
        
        serializer = WorkflowImpactSerializer(impact_data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def available_workflows(self, request):
        """
        Get available workflow types for a tenant.
        """
        tenant_id = request.query_params.get('tenant_id')
        if not tenant_id:
            return Response(
                {'error': 'tenant_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            tenant = Tenant.objects.get(id=tenant_id)
        except Tenant.DoesNotExist:
            return Response(
                {'error': 'Tenant not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Determine available workflows based on tenant type
        available_workflows = ['centralized']
        
        if tenant.type == 'kebele':
            available_workflows.append('autonomous')
        
        return Response({
            'tenant': {
                'id': tenant.id,
                'name': tenant.name,
                'type': tenant.type
            },
            'available_workflows': [
                {
                    'type': 'centralized',
                    'name': 'Centralized Workflow',
                    'description': 'Standard hierarchical workflow with subcity approval and printing',
                    'suitable_for': ['kebele', 'subcity', 'city']
                },
                {
                    'type': 'autonomous',
                    'name': 'Autonomous Workflow', 
                    'description': 'Self-sufficient kebele handling all processes locally',
                    'suitable_for': ['kebele']
                }
            ] if tenant.type == 'kebele' else [
                {
                    'type': 'centralized',
                    'name': 'Centralized Workflow',
                    'description': 'Standard hierarchical workflow',
                    'suitable_for': ['kebele', 'subcity', 'city']
                }
            ]
        })
    
    def _can_manage_tenant_workflow(self, user, tenant):
        """Check if user can manage workflow for the given tenant."""
        if user.is_superuser:
            return True
        
        if user.role == 'subcity_admin':
            # Subcity admins can manage kebeles in their subcity
            if tenant.type == 'kebele' and hasattr(user, 'tenant'):
                return tenant.parent_id == user.tenant.id
            # Can also manage their own subcity
            return tenant.id == getattr(user.tenant, 'id', None)
        
        if user.role == 'kebele_leader':
            # Kebele leaders can only manage their own kebele
            return (tenant.type == 'kebele' and 
                   hasattr(user, 'tenant') and 
                   tenant.id == user.tenant.id)
        
        return False
    
    def _analyze_workflow_impact(self, tenant, target_workflow):
        """Analyze the impact of switching to target workflow."""
        current_config = TenantWorkflowConfig.objects.filter(tenant=tenant).first()
        current_workflow = current_config.workflow_type if current_config else 'centralized'
        
        # Count pending ID cards that would be affected
        with schema_context(tenant.schema_name):
            from idcards.models import IDCard, IDCardStatus
            
            pending_cards = IDCard.objects.filter(
                status__in=[IDCardStatus.PENDING_APPROVAL, IDCardStatus.KEBELE_APPROVED]
            ).count()
            
            approved_cards = IDCard.objects.filter(
                status=IDCardStatus.APPROVED
            ).count()
            
            users = User.objects.filter(tenant=tenant, is_active=True)
            user_count_by_role = {}
            for user in users:
                role = user.role
                user_count_by_role[role] = user_count_by_role.get(role, 0) + 1
        
        # Determine changes needed
        changes_needed = []
        if current_workflow != target_workflow:
            if target_workflow == 'autonomous':
                changes_needed = [
                    'Enable local ID card printing',
                    'Grant final approval authority to kebele leader',
                    'Apply security patterns locally',
                    'Update user group permissions',
                    'Train staff on autonomous workflow'
                ]
            else:  # centralized
                changes_needed = [
                    'Disable local ID card printing',
                    'Route approvals through subcity admin',
                    'Centralize security pattern application',
                    'Update user group permissions',
                    'Coordinate with subcity office'
                ]
        
        return {
            'tenant': tenant.name,
            'current_workflow': current_workflow,
            'target_workflow': target_workflow,
            'requires_change': current_workflow != target_workflow,
            'pending_id_cards': pending_cards,
            'approved_id_cards': approved_cards,
            'user_count_by_role': user_count_by_role,
            'changes_needed': changes_needed,
            'estimated_impact': 'High' if pending_cards > 10 else 'Medium' if pending_cards > 0 else 'Low'
        }
