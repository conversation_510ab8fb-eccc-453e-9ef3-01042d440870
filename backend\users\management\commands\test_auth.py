from django.core.management.base import BaseCommand
from django.contrib.auth import authenticate
from users.models import User
from tenants.models import Tenant
from django_tenants.utils import schema_context
from rest_framework.test import APIClient
from django.db import transaction


class Command(BaseCommand):
    help = 'Test authentication for different user types'

    def handle(self, *args, **options):
        self.stdout.write('🔍 Testing Authentication System...\n')

        # Fix additional_roles field first
        self.fix_additional_roles()

        # Test superadmin authentication
        self.test_superadmin_auth()

        # Create and test regular user
        self.test_regular_user_auth()

        self.stdout.write('\n✅ Authentication testing completed')

    def fix_additional_roles(self):
        """Fix additional_roles field for all users"""
        self.stdout.write('🔧 Fixing additional_roles field...')
        
        try:
            with transaction.atomic():
                # Update users with NULL additional_roles
                null_count = User.objects.filter(additional_roles__isnull=True).count()
                if null_count > 0:
                    updated = User.objects.filter(additional_roles__isnull=True).update(additional_roles=[])
                    self.stdout.write(f'   ✅ Updated {updated} users with NULL additional_roles')
                
                # Check for users with invalid additional_roles
                fixed_count = 0
                for user in User.objects.all():
                    try:
                        roles = user.additional_roles
                        if not isinstance(roles, list):
                            user.additional_roles = []
                            user.save()
                            fixed_count += 1
                    except Exception:
                        user.additional_roles = []
                        user.save()
                        fixed_count += 1
                
                if fixed_count > 0:
                    self.stdout.write(f'   ✅ Fixed {fixed_count} users with invalid additional_roles')
                else:
                    self.stdout.write('   ✅ All users have valid additional_roles')
                    
        except Exception as e:
            self.stdout.write(f'   ❌ Error fixing additional_roles: {e}')

    def test_superadmin_auth(self):
        """Test superadmin authentication"""
        self.stdout.write('\n🔐 Testing Superadmin Authentication...')
        
        try:
            # Test Django authenticate
            user = authenticate(email='<EMAIL>', password='admin123')
            if user:
                self.stdout.write(f'   ✅ Django auth successful: {user.email}')
            else:
                self.stdout.write('   ❌ Django auth failed')
                return

            # Test API authentication
            client = APIClient()
            response = client.post('/api/auth/token/', {
                'email': '<EMAIL>',
                'password': 'admin123'
            }, format='json')

            if response.status_code == 200:
                self.stdout.write('   ✅ API auth successful')
                self.stdout.write(f'   📋 Response keys: {list(response.data.keys())}')
            else:
                self.stdout.write(f'   ❌ API auth failed: {response.status_code}')
                self.stdout.write(f'   📋 Error: {response.data}')

        except Exception as e:
            self.stdout.write(f'   ❌ Error testing superadmin auth: {e}')

    def test_regular_user_auth(self):
        """Create and test regular user authentication"""
        self.stdout.write('\n👤 Testing Regular User Authentication...')
        
        try:
            # Get a kebele tenant
            kebele_tenant = Tenant.objects.filter(type='kebele').first()
            if not kebele_tenant:
                self.stdout.write('   ❌ No kebele tenant found')
                return

            self.stdout.write(f'   🏢 Using tenant: {kebele_tenant.name}')

            # Create test user in kebele schema
            with schema_context(kebele_tenant.schema_name):
                test_email = '<EMAIL>'
                
                # Delete existing test user if exists
                User.objects.filter(email=test_email).delete()
                
                # Create new test user
                test_user = User.objects.create_user(
                    email=test_email,
                    username='test_clerk',
                    password='test123',
                    first_name='Test',
                    last_name='Clerk',
                    role='clerk',
                    tenant=kebele_tenant,
                    is_active=True,
                    is_superuser=False
                )
                
                # Ensure additional_roles is set
                test_user.additional_roles = []
                test_user.save()
                
                self.stdout.write(f'   ✅ Created test user: {test_user.email}')

                # Test Django authenticate
                auth_user = authenticate(email=test_email, password='test123')
                if auth_user:
                    self.stdout.write('   ✅ Django auth successful for regular user')
                else:
                    self.stdout.write('   ❌ Django auth failed for regular user')

                # Test API authentication
                client = APIClient()
                response = client.post('/api/auth/token/', {
                    'email': test_email,
                    'password': 'test123'
                }, format='json')

                if response.status_code == 200:
                    self.stdout.write('   ✅ API auth successful for regular user')
                    self.stdout.write(f'   📋 Response keys: {list(response.data.keys())}')
                else:
                    self.stdout.write(f'   ❌ API auth failed for regular user: {response.status_code}')
                    self.stdout.write(f'   📋 Error: {response.data}')

                # Clean up
                test_user.delete()
                self.stdout.write('   🧹 Cleaned up test user')

        except Exception as e:
            self.stdout.write(f'   ❌ Error testing regular user auth: {e}')
            import traceback
            traceback.print_exc()

    def test_user_field_access(self):
        """Test if we can access user fields without errors"""
        self.stdout.write('\n🔍 Testing User Field Access...')
        
        try:
            users = User.objects.all()[:3]
            for user in users:
                self.stdout.write(f'   User: {user.email}')
                
                # Test field access
                try:
                    role = user.role
                    self.stdout.write(f'     Role: {role}')
                except Exception as e:
                    self.stdout.write(f'     ❌ Error accessing role: {e}')
                
                try:
                    additional_roles = user.additional_roles
                    self.stdout.write(f'     Additional roles: {additional_roles}')
                except Exception as e:
                    self.stdout.write(f'     ❌ Error accessing additional_roles: {e}')
                
                try:
                    tenant = user.tenant
                    tenant_name = tenant.name if tenant else 'None'
                    self.stdout.write(f'     Tenant: {tenant_name}')
                except Exception as e:
                    self.stdout.write(f'     ❌ Error accessing tenant: {e}')

        except Exception as e:
            self.stdout.write(f'   ❌ Error testing field access: {e}')
