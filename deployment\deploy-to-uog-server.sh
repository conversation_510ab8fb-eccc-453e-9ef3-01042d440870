#!/bin/bash
# GoID Deployment Script for UoG Server
# Specifically configured for aragawmebratu/goid-production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Configuration for UoG Server
DOCKER_HUB_USERNAME="aragawmebratu"
SERVER_IP="************"
DOMAIN="goid.uog.edu.et"
ENV_FILE="deployment/.env.production"

print_header "GoID Deployment to UoG Server"
print_status "Server IP: $SERVER_IP"
print_status "Domain: $DOMAIN"
print_status "Docker Hub: $DOCKER_HUB_USERNAME/goid-production"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Step 1: Build and push images
print_header "Step 1: Building and Pushing Images"

print_status "Building backend service..."
if ! ./deployment/build.sh --service backend --username "$DOCKER_HUB_USERNAME"; then
    print_error "Failed to build backend service"
    exit 1
fi

print_status "Building frontend service..."
if ! ./deployment/build.sh --service frontend --username "$DOCKER_HUB_USERNAME"; then
    print_error "Failed to build frontend service"
    exit 1
fi

print_success "All images built and pushed successfully!"

# Step 2: Create production environment file
print_header "Step 2: Creating Production Environment"

if [[ ! -f "$ENV_FILE" ]]; then
    print_status "Creating production environment file..."
    cat > "$ENV_FILE" << EOF
# GoID Production Environment for UoG Server

# Docker Hub Configuration
DOCKER_HUB_USERNAME=aragawmebratu

# Service Versions
BACKEND_VERSION=latest
FRONTEND_VERSION=latest

# Database Configuration
DB_PASSWORD=GoID_UoG_2024_SecureDB!
DB_PORT=5432

# Redis Configuration
REDIS_PORT=6379

# Application Configuration
SECRET_KEY=GoID_UoG_Production_Secret_Key_2024_Very_Long_And_Secure_String_Change_This
ALLOWED_HOSTS=************,goid.uog.edu.et,localhost,127.0.0.1
CORS_ALLOWED_ORIGINS=http://************:3000,https://goid.uog.edu.et,http://goid.uog.edu.et:3000

# API URLs
REACT_APP_API_URL=http://************:8000
REACT_APP_BIOMETRIC_SERVICE_URL=http://localhost:8002

# Admin Configuration
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=UoG_Admin_2024!
PGADMIN_PORT=5050
EOF
    
    print_success "Production environment file created"
    print_warning "Please review and update passwords in $ENV_FILE if needed"
else
    print_status "Using existing environment file: $ENV_FILE"
fi

# Step 3: Generate docker-compose file
print_header "Step 3: Generating Docker Compose Configuration"

print_status "Generating docker-compose.production.yml..."
if ! ./deployment/deploy.sh generate --env-file "$ENV_FILE"; then
    print_error "Failed to generate docker-compose file"
    exit 1
fi

# Step 4: Deploy services
print_header "Step 4: Deploying Services"

print_status "Pulling latest images from Docker Hub..."
if ! ./deployment/deploy.sh pull --env-file "$ENV_FILE"; then
    print_warning "Failed to pull some images, continuing with deployment..."
fi

print_status "Deploying services to UoG server..."
if ! ./deployment/deploy.sh deploy --env-file "$ENV_FILE"; then
    print_error "Failed to deploy services"
    exit 1
fi

# Step 5: Wait for services to start
print_header "Step 5: Verifying Deployment"

print_status "Waiting for services to start..."
sleep 30

print_status "Checking service status..."
./deployment/deploy.sh status --env-file "$ENV_FILE"

# Step 6: Test endpoints
print_header "Step 6: Testing Endpoints"

print_status "Testing backend health..."
if curl -f -s "http://$SERVER_IP:8000/api/health/" > /dev/null; then
    print_success "Backend is responding"
else
    print_warning "Backend health check failed - may still be starting"
fi

print_status "Testing frontend..."
if curl -f -s "http://$SERVER_IP:3000/" > /dev/null; then
    print_success "Frontend is responding"
else
    print_warning "Frontend health check failed - may still be starting"
fi

# Step 7: Show deployment summary
print_header "Deployment Complete"

print_success "GoID system deployed successfully to UoG server!"
print_status ""
print_status "🌐 Access URLs:"
print_status "  Frontend (IP):   http://$SERVER_IP:3000"
print_status "  Frontend (Domain): https://$DOMAIN (configure SSL)"
print_status "  Backend API:     http://$SERVER_IP:8000"
print_status "  Database:        $SERVER_IP:5432"
print_status "  pgAdmin:         http://$SERVER_IP:5050"
print_status ""
print_status "📋 Next Steps:"
print_status "  1. Configure SSL certificate for $DOMAIN"
print_status "  2. Set up firewall rules for ports 3000, 8000, 5432, 5050"
print_status "  3. Install biometric service on client machines"
print_status "  4. Configure kebele information in admin panel"
print_status "  5. Test fingerprint capture functionality"
print_status ""
print_status "🔧 Management Commands:"
print_status "  Check status:  ./deployment/deploy.sh status"
print_status "  View logs:     ./deployment/deploy.sh logs"
print_status "  Stop services: ./deployment/deploy.sh stop"
print_status "  Restart:       ./deployment/deploy.sh deploy"
print_status ""
print_status "📁 Client Installation:"
print_status "  Build installer: cd deployment/client-biometric && python build_installer.py"
print_status "  Distribute to kebele offices and run installer"
print_status ""
print_status "🔐 Security Notes:"
print_status "  - Change default passwords in $ENV_FILE"
print_status "  - Configure SSL/TLS for production"
print_status "  - Set up regular database backups"
print_status "  - Monitor system logs regularly"

print_header "UoG GoID System Ready!"
