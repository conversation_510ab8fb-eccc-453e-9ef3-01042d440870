"""
Utility functions for managing group permissions and navigation access.
"""

from django.contrib.auth.models import Permission, Group
from django.contrib.contenttypes.models import ContentType
from django.db import connection
from django_tenants.utils import schema_context, get_public_schema_name
from users.models_groups import TenantGroup
import logging

logger = logging.getLogger(__name__)


def get_permission_sets():
    """
    Define permission sets for different management levels.
    
    Returns:
        dict: Permission sets for different management types
    """
    return {
        'kebele_management': {
            'description': 'Kebele User Management Group',
            'permissions': [
                # Basic navigation
                'users.navigate_to_dashboard',
                'users.view_kebele_dashboard',
                'users.navigate_to_citizens',
                'users.view_citizens_list',
                'users.view_citizen_details',
                
                # User management
                'users.view_user_management',
                'users.view_kebele_management',
                'users.create_kebele_users',
                'users.view_user',
                'users.change_user',
                
                # ID Cards
                'users.navigate_to_id_cards',
                'users.view_id_cards_list',
                'users.generate_id_cards',
                'users.send_id_cards_for_approval',
                
                # Reports
                'users.view_kebele_reports',
                'users.view_own_kebele_data',
                
                # Navigation menus
                'users.access_management_menu',
                'users.manage_kebele_navigation',
            ]
        },
        
        'subcity_management': {
            'description': 'Subcity User Management Group',
            'permissions': [
                # Basic navigation
                'users.navigate_to_dashboard',
                'users.view_subcity_dashboard',
                'users.navigate_to_citizens',
                'users.view_citizens_list',
                'users.view_citizen_details',
                
                # User management
                'users.view_user_management',
                'users.view_subcity_management',
                'users.view_kebele_management',
                'users.create_kebele_users',
                'users.create_subcity_users',
                'users.view_user',
                'users.change_user',
                
                # ID Cards
                'users.navigate_to_id_cards',
                'users.view_id_cards_list',
                'users.approve_id_cards',
                'users.print_id_cards',
                'users.send_id_cards_to_higher_level',
                
                # Data access
                'users.view_child_kebeles_data',
                'users.view_subcity_reports',
                
                # Workflows
                'users.navigate_to_workflows',
                'users.view_workflows',
                'users.manage_workflows',
                
                # Navigation menus
                'users.access_management_menu',
                'users.access_admin_menu',
                'users.manage_subcity_navigation',
            ]
        },
        
        'city_management': {
            'description': 'City User Management Group',
            'permissions': [
                # Basic navigation
                'users.navigate_to_dashboard',
                'users.view_city_dashboard',
                'users.navigate_to_citizens',
                'users.view_citizens_list',
                'users.view_citizen_details',
                
                # User management
                'users.view_user_management',
                'users.view_city_management',
                'users.view_subcity_management',
                'users.create_subcity_users',
                'users.view_user',
                'users.change_user',
                
                # Data access
                'users.view_child_subcities_data',
                'users.view_city_reports',
                'users.view_all_reports',
                
                # Navigation menus
                'users.access_management_menu',
                'users.access_admin_menu',
                'users.access_reports_menu',
                'users.manage_city_navigation',
                
                # Reports
                'users.navigate_to_reports',
            ]
        },
        
        'printer_only': {
            'description': 'ID Card Printing Only',
            'permissions': [
                # Basic navigation
                'users.navigate_to_dashboard',
                'users.view_kebele_dashboard',
                
                # ID Cards only
                'users.navigate_to_id_cards',
                'users.view_id_cards_list',
                'users.print_id_cards',
            ]
        },
        
        'clerk_basic': {
            'description': 'Basic Clerk Permissions',
            'permissions': [
                # Basic navigation
                'users.navigate_to_dashboard',
                'users.view_kebele_dashboard',
                'users.navigate_to_citizens',

                # Citizen management
                'users.view_citizens_list',
                'users.view_citizen_details',
                'users.add_citizen',
                'users.change_citizen',
                'users.register_citizens',

                # ID Cards
                'users.navigate_to_id_cards',
                'users.view_id_cards_list',
                'users.generate_id_cards',
            ]
        },

        # Workflow-specific group templates
        'centralized_clerk': {
            'description': 'Centralized Workflow Clerk',
            'permissions': [
                # Basic navigation
                'users.navigate_to_dashboard',
                'users.view_kebele_dashboard',
                'users.navigate_to_citizens',

                # Citizen management
                'users.view_citizens_list',
                'users.view_citizen_details',
                'users.add_citizen',
                'users.change_citizen',
                'users.register_citizens',

                # ID Cards - Centralized workflow
                'users.navigate_to_id_cards',
                'users.view_id_cards_list',
                'users.generate_id_cards',
                'users.send_id_cards_for_approval',  # Send to kebele leader
                'users.view_own_kebele_data',
            ]
        },

        'autonomous_clerk': {
            'description': 'Autonomous Workflow Clerk',
            'permissions': [
                # Basic navigation
                'users.navigate_to_dashboard',
                'users.view_kebele_dashboard',
                'users.navigate_to_citizens',

                # Citizen management
                'users.view_citizens_list',
                'users.view_citizen_details',
                'users.add_citizen',
                'users.change_citizen',
                'users.register_citizens',

                # ID Cards - Autonomous workflow
                'users.navigate_to_id_cards',
                'users.view_id_cards_list',
                'users.generate_id_cards',
                'users.send_id_cards_for_approval',  # Send to kebele leader
                'users.print_id_cards',  # Can print after kebele leader approval
                'users.view_own_kebele_data',
            ]
        },

        'centralized_kebele_leader': {
            'description': 'Centralized Workflow Kebele Leader',
            'permissions': [
                # Basic navigation
                'users.navigate_to_dashboard',
                'users.view_kebele_dashboard',
                'users.navigate_to_citizens',

                # Citizen and ID card management
                'users.view_citizens_list',
                'users.view_citizen_details',
                'users.navigate_to_id_cards',
                'users.view_id_cards_list',
                'users.approve_id_cards',  # First approval
                'users.send_id_cards_to_higher_level',  # Send to subcity
                'users.verify_documents',
                'users.view_kebele_reports',
                'users.view_own_kebele_data',

                # User management
                'users.view_user_management',
                'users.view_kebele_management',
                'users.create_kebele_users',
            ]
        },

        'autonomous_kebele_leader': {
            'description': 'Autonomous Workflow Kebele Leader',
            'permissions': [
                # Basic navigation
                'users.navigate_to_dashboard',
                'users.view_kebele_dashboard',
                'users.navigate_to_citizens',

                # Citizen and ID card management
                'users.view_citizens_list',
                'users.view_citizen_details',
                'users.navigate_to_id_cards',
                'users.view_id_cards_list',
                'users.approve_id_cards',  # Final approval (no subcity needed)
                'users.print_id_cards',  # Can print after approval
                'users.apply_security_patterns',  # Apply patterns locally
                'users.verify_documents',
                'users.view_kebele_reports',
                'users.view_own_kebele_data',

                # User management
                'users.view_user_management',
                'users.view_kebele_management',
                'users.create_kebele_users',

                # Workflow management
                'users.manage_kebele_workflow',
                'users.switch_workflow_mode',
            ]
        },

        'designated_printer': {
            'description': 'Designated ID Card Printer',
            'permissions': [
                # Basic navigation
                'users.navigate_to_dashboard',
                'users.view_kebele_dashboard',

                # ID Cards printing only
                'users.navigate_to_id_cards',
                'users.view_id_cards_list',
                'users.print_id_cards',
                'users.view_printing_queue',
                'users.manage_printer_settings',
            ]
        }
    }


def create_management_group(group_name, permission_set_name, tenant=None, description=None):
    """
    Create a management group with appropriate navigation permissions.
    
    Args:
        group_name (str): Name of the group to create
        permission_set_name (str): Name of the permission set to apply
        tenant: Tenant instance (optional)
        description (str): Custom description (optional)
        
    Returns:
        tuple: (success, group_or_error_message, tenant_group)
    """
    try:
        with schema_context(get_public_schema_name()):
            # Get permission set
            permission_sets = get_permission_sets()
            if permission_set_name not in permission_sets:
                return False, f"Permission set '{permission_set_name}' not found", None
            
            permission_set = permission_sets[permission_set_name]
            
            # Create Django group
            group, created = Group.objects.get_or_create(name=group_name)
            
            if created:
                logger.info(f"Created new Django group: {group_name}")
            else:
                logger.info(f"Using existing Django group: {group_name}")
            
            # Add permissions to group
            permissions_added = add_permissions_to_group(group, permission_set['permissions'])
            
            # Create TenantGroup if tenant is provided
            tenant_group = None
            if tenant:
                tenant_group, tg_created = TenantGroup.objects.get_or_create(
                    group=group,
                    tenant=tenant,
                    defaults={
                        'description': description or permission_set['description'],
                        'group_type': 'management',
                        'level': 30,  # Management level
                        'is_system_group': False,
                        'is_active': True
                    }
                )
                
                if tg_created:
                    logger.info(f"Created TenantGroup for {group_name} in tenant {tenant.name}")
            
            return True, group, tenant_group
            
    except Exception as e:
        logger.error(f"Error creating management group {group_name}: {e}")
        return False, str(e), None


def add_permissions_to_group(group, permission_list):
    """
    Add a list of permissions to a group.
    
    Args:
        group: Django Group instance
        permission_list: List of permission strings (e.g., ['users.view_dashboard'])
        
    Returns:
        int: Number of permissions added
    """
    permissions_added = 0
    
    for perm_string in permission_list:
        try:
            app_label, codename = perm_string.split('.', 1)
            permission = Permission.objects.get(
                content_type__app_label=app_label,
                codename=codename
            )
            
            # Add permission if not already present
            if not group.permissions.filter(id=permission.id).exists():
                group.permissions.add(permission)
                permissions_added += 1
                logger.debug(f"Added permission {perm_string} to group {group.name}")
            
        except Permission.DoesNotExist:
            logger.warning(f"Permission {perm_string} not found")
        except ValueError:
            logger.warning(f"Invalid permission format: {perm_string}")
    
    logger.info(f"Added {permissions_added} permissions to group {group.name}")
    return permissions_added


def update_group_permissions(group_name, permission_set_name):
    """
    Update an existing group's permissions with a new permission set.
    
    Args:
        group_name (str): Name of the group to update
        permission_set_name (str): Name of the permission set to apply
        
    Returns:
        tuple: (success, message)
    """
    try:
        with schema_context(get_public_schema_name()):
            # Get the group
            group = Group.objects.get(name=group_name)
            
            # Get permission set
            permission_sets = get_permission_sets()
            if permission_set_name not in permission_sets:
                return False, f"Permission set '{permission_set_name}' not found"
            
            permission_set = permission_sets[permission_set_name]
            
            # Clear existing permissions
            group.permissions.clear()
            logger.info(f"Cleared existing permissions for group {group_name}")
            
            # Add new permissions
            permissions_added = add_permissions_to_group(group, permission_set['permissions'])
            
            return True, f"Updated group '{group_name}' with {permissions_added} permissions"
            
    except Group.DoesNotExist:
        return False, f"Group '{group_name}' not found"
    except Exception as e:
        return False, f"Error updating group: {str(e)}"


def get_group_navigation_permissions(group_name):
    """
    Get navigation permissions for a specific group.
    
    Args:
        group_name (str): Name of the group
        
    Returns:
        list: List of navigation permission strings
    """
    try:
        with schema_context(get_public_schema_name()):
            group = Group.objects.get(name=group_name)
            
            # Get navigation-related permissions
            nav_keywords = ['navigate', 'view', 'access', 'manage']
            nav_perms = group.permissions.filter(
                codename__iregex=r'(' + '|'.join(nav_keywords) + ')'
            ).values_list('content_type__app_label', 'codename')
            
            return [f"{app}.{codename}" for app, codename in nav_perms]
            
    except Group.DoesNotExist:
        return []
    except Exception as e:
        logger.error(f"Error getting navigation permissions for {group_name}: {e}")
        return []


def list_available_permission_sets():
    """
    List all available permission sets.
    
    Returns:
        dict: Available permission sets with descriptions
    """
    permission_sets = get_permission_sets()
    return {
        name: {
            'description': pset['description'],
            'permission_count': len(pset['permissions'])
        }
        for name, pset in permission_sets.items()
    }
