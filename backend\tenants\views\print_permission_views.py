from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db import transaction
from django_tenants.utils import schema_context
from tenants.models import Tenant
from users.models import User, UserRole
from users.serializers import UserSerializer


class PrintPermissionViewSet(viewsets.ViewSet):
    """
    ViewSet for managing print permissions in autonomous workflows.
    Only kebele leaders can assign/revoke print permissions.
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_tenant(self):
        """Get tenant from URL parameter."""
        tenant_id = self.kwargs.get('tenant_id')
        if not tenant_id:
            raise ValueError("Tenant ID is required")
        return Tenant.objects.get(id=tenant_id)

    def has_permission(self, request, view):
        """Check if user has permission to manage print permissions."""
        user = request.user
        
        # Superusers have full permission
        if user.is_superuser:
            return True
        
        # Only kebele leaders and admins can manage print permissions
        if user.role not in ['kebele_leader', 'kebele_admin']:
            return False
        
        # Check if user belongs to the target tenant
        try:
            tenant = self.get_tenant()
            return user.tenant and user.tenant.id == tenant.id
        except:
            return False

    @action(detail=False, methods=['get'])
    def list_users(self, request, tenant_id=None):
        """
        List all users in the kebele who can be assigned print permissions.
        """
        if not self.has_permission(request, self):
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            # Get all users in the tenant except superadmins
            users = User.objects.filter(
                tenant=tenant,
                is_active=True
            ).exclude(role='superadmin')

            # Add print permission status to each user
            users_data = []
            for user in users:
                additional_roles = getattr(user, 'additional_roles', [])
                has_print_permission = (
                    'designated_printer' in additional_roles or
                    user.role in ['kebele_leader', 'kebele_admin']
                )
                
                user_data = UserSerializer(user).data
                user_data['has_print_permission'] = has_print_permission
                user_data['can_print_reason'] = self._get_print_reason(user, additional_roles)
                users_data.append(user_data)

            return Response({
                'users': users_data,
                'tenant': {
                    'id': tenant.id,
                    'name': tenant.name,
                    'type': tenant.type
                }
            })

    @action(detail=False, methods=['post'])
    def assign_print_permission(self, request, tenant_id=None):
        """
        Assign print permission to a user.
        """
        if not self.has_permission(request, self):
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        user_id = request.data.get('user_id')
        if not user_id:
            return Response(
                {'error': 'user_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            try:
                user = User.objects.get(id=user_id, tenant=tenant)
            except User.DoesNotExist:
                return Response(
                    {'error': 'User not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Check if user already has print permission
            additional_roles = getattr(user, 'additional_roles', [])
            if 'designated_printer' in additional_roles:
                return Response(
                    {'message': 'User already has print permission'},
                    status=status.HTTP_200_OK
                )

            # Assign print permission
            with transaction.atomic():
                additional_roles.append('designated_printer')
                user.additional_roles = additional_roles
                user.save()

                return Response({
                    'message': f'Print permission assigned to {user.get_full_name()}',
                    'user': UserSerializer(user).data
                })

    @action(detail=False, methods=['post'])
    def revoke_print_permission(self, request, tenant_id=None):
        """
        Revoke print permission from a user.
        """
        if not self.has_permission(request, self):
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        user_id = request.data.get('user_id')
        if not user_id:
            return Response(
                {'error': 'user_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            try:
                user = User.objects.get(id=user_id, tenant=tenant)
            except User.DoesNotExist:
                return Response(
                    {'error': 'User not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Check if user has designated_printer role
            additional_roles = getattr(user, 'additional_roles', [])
            if 'designated_printer' not in additional_roles:
                return Response(
                    {'message': 'User does not have designated printer permission'},
                    status=status.HTTP_200_OK
                )

            # Don't allow revoking from kebele leaders/admins
            if user.role in ['kebele_leader', 'kebele_admin']:
                return Response(
                    {'error': 'Cannot revoke print permission from kebele leaders/admins'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Revoke print permission
            with transaction.atomic():
                additional_roles.remove('designated_printer')
                user.additional_roles = additional_roles
                user.save()

                return Response({
                    'message': f'Print permission revoked from {user.get_full_name()}',
                    'user': UserSerializer(user).data
                })

    def _get_print_reason(self, user, additional_roles):
        """Get reason why user can or cannot print."""
        if user.role in ['kebele_leader', 'kebele_admin']:
            return f"Has print permission through {user.role} role"
        elif 'designated_printer' in additional_roles:
            return "Assigned as designated printer"
        else:
            return "No print permission assigned"

    @action(detail=False, methods=['get'])
    def workflow_status(self, request, tenant_id=None):
        """
        Get workflow configuration status for the tenant.
        """
        if not self.has_permission(request, self):
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        tenant = self.get_tenant()
        
        # Get workflow configuration
        workflow_config = getattr(tenant, 'workflow_config', None)
        if workflow_config:
            id_card_config = workflow_config.id_card_processing
            return Response({
                'tenant': {
                    'id': tenant.id,
                    'name': tenant.name,
                    'type': tenant.type
                },
                'workflow_type': workflow_config.workflow_type,
                'can_print_locally': id_card_config.get('can_print_locally', False),
                'granular_print_permissions': id_card_config.get('granular_print_permissions', False),
                'print_roles': id_card_config.get('print_roles', []),
                'printing_authority': id_card_config.get('printing_authority', 'subcity')
            })
        else:
            return Response({
                'tenant': {
                    'id': tenant.id,
                    'name': tenant.name,
                    'type': tenant.type
                },
                'workflow_type': 'centralized',
                'can_print_locally': False,
                'granular_print_permissions': False,
                'print_roles': [],
                'printing_authority': 'subcity'
            })
