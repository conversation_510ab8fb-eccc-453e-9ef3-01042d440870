from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from users.serializers import CustomTokenObtainPairSerializer
import jwt
from django.conf import settings

User = get_user_model()


class Command(BaseCommand):
    help = 'Test JWT token generation for city_system_admin users'

    def handle(self, *args, **options):
        self.stdout.write('🧪 Testing city_system_admin JWT token generation...')
        
        # Find city_system_admin users
        city_sys_admins = User.objects.filter(role='city_system_admin')
        self.stdout.write(f'Found {city_sys_admins.count()} city_system_admin users')
        
        if city_sys_admins.count() == 0:
            self.stdout.write(self.style.WARNING('No city_system_admin users found'))
            return
        
        for user in city_sys_admins:
            self.stdout.write(f'\n🔍 Testing user: {user.email}')
            self.stdout.write(f'  Database role: {user.role}')
            self.stdout.write(f'  Tenant: {user.tenant.name} (Type: {user.tenant.type})')
            
            try:
                # Test JWT token generation
                serializer = CustomTokenObtainPairSerializer()
                token = serializer.get_token(user)
                
                # Decode the token to see what's inside
                decoded = jwt.decode(str(token.access_token), settings.SECRET_KEY, algorithms=['HS256'])
                
                self.stdout.write(f'  ✅ JWT generated successfully')
                self.stdout.write(f'  JWT role: {decoded.get("role", "NOT_FOUND")}')
                self.stdout.write(f'  JWT permissions: {decoded.get("permissions", [])}')
                self.stdout.write(f'  JWT groups: {decoded.get("groups", [])}')
                
                # Check if role was changed
                if decoded.get('role') != user.role:
                    self.stdout.write(self.style.ERROR(f'  ❌ Role changed from {user.role} to {decoded.get("role")}'))
                else:
                    self.stdout.write(self.style.SUCCESS(f'  ✅ Role preserved: {user.role}'))
                
                # Check if correct permissions are assigned
                expected_perms = ['create_subcity_users', 'view_user_management', 'navigate_to_dashboard']
                actual_perms = decoded.get('permissions', [])
                
                if 'create_subcity_users' in actual_perms:
                    self.stdout.write(self.style.SUCCESS(f'  ✅ Has create_subcity_users permission'))
                else:
                    self.stdout.write(self.style.ERROR(f'  ❌ Missing create_subcity_users permission'))
                    self.stdout.write(f'  Expected: {expected_perms}')
                    self.stdout.write(f'  Actual: {actual_perms}')
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'  ❌ Error generating JWT: {e}'))
                import traceback
                traceback.print_exc()
        
        self.stdout.write(f'\n✅ JWT testing completed!')
