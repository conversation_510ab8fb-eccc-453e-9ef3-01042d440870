from rest_framework import permissions
from .models.tenant import TenantType


class IsSuperUser(permissions.BasePermission):
    """
    Allows access only to superusers.
    """
    def has_permission(self, request, view):
        return request.user and request.user.is_superuser


class CanManageTenants(permissions.BasePermission):
    """
    Permission class for managing tenants.

    - City admins can create and manage subcity tenants
    - Subcity admins can create and manage kebele tenants
    - Super admins can manage all tenants
    """
    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers and superadmins have full permission
        if user.is_superuser:
            return True

        # Check if user has superadmin role (safely handle AnonymousUser)
        if hasattr(user, 'role') and user.role == 'superadmin':
            return True

        # For safe methods (GET, HEAD, OPTIONS), city and subcity admins have permission
        if request.method in permissions.SAFE_METHODS:
            if hasattr(user, 'role') and user.role in ['city_admin', 'city_system_admin', 'subcity_admin', 'subcity_system_admin']:
                return True

        # For unsafe methods (POST, PUT, PATCH, DELETE), check role-specific permissions
        if hasattr(user, 'role') and user.role in ['city_admin', 'city_system_admin']:
            # City admins and city system admins can create/edit subcity tenants
            return True
        elif hasattr(user, 'role') and user.role in ['subcity_admin', 'subcity_system_admin']:
            # Subcity admins and subcity system admins can create/edit kebele tenants
            return True

        return False

    def has_object_permission(self, request, view, obj):
        user = request.user

        # Superusers and superadmins have full permission
        if user.is_superuser:
            return True

        # Check if user has superadmin role (safely handle AnonymousUser)
        if hasattr(user, 'role') and user.role == 'superadmin':
            return True

        # Check role-based permissions
        if hasattr(user, 'role') and user.role == 'city_admin':
            # City admins can manage their own city tenant (for creating city system admins)
            if hasattr(obj, 'type') and obj.type == 'city' and obj == getattr(user, 'tenant', None):
                return True
            # City admins can manage subcity tenants in their city
            if hasattr(obj, 'type') and obj.type == 'subcity' and hasattr(obj, 'parent') and obj.parent == getattr(user, 'tenant', None):
                return True
            # City admins can view kebele tenants in their city hierarchy
            if hasattr(obj, 'type') and obj.type == 'kebele' and hasattr(obj, 'parent') and obj.parent and hasattr(obj.parent, 'parent') and obj.parent.parent == getattr(user, 'tenant', None):
                return request.method in permissions.SAFE_METHODS
        elif hasattr(user, 'role') and user.role in ['subcity_admin', 'subcity_system_admin']:
            # Subcity admins and subcity system admins can manage kebele tenants in their subcity
            if hasattr(obj, 'type') and obj.type == 'kebele' and hasattr(obj, 'parent') and obj.parent == getattr(user, 'tenant', None):
                return True

        return False


class CanRegisterTenants(permissions.BasePermission):
    """
    Permission class specifically for tenant registration.
    Only superusers and superadmins can register tenants.
    """
    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Only superusers and superadmins can register tenants
        if user.is_superuser:
            return True

        # Check if user has superadmin role (safely handle AnonymousUser)
        if hasattr(user, 'role') and user.role == 'superadmin':
            return True

        return False