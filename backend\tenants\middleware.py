import re
from django.http import JsonResponse
from django.db import connections
from django_tenants.utils import get_tenant_model, get_tenant_domain_model
from django_tenants.middleware import TenantMainMiddleware


class CustomTenantMiddleware(TenantMainMiddleware):
    """
    Custom middleware to handle tenant switching based on request headers.
    """

    def process_request(self, request):
        print(f"🔍 MIDDLEWARE: Processing request {request.method} {request.path}")
        print(f"  User: {getattr(request, 'user', 'NO_USER')}")
        print(f"  Has Authorization header: {'HTTP_AUTHORIZATION' in request.META}")

        # Special logging for ALL transfer requests
        if '/api/tenants/transfers/' in request.path:
            print(f"🚨 TRANSFER REQUEST DETECTED:")
            print(f"  Path: {request.path}")
            print(f"  Method: {request.method}")
            user = getattr(request, 'user', None)
            print(f"  User authenticated: {user.is_authenticated if user else False}")
            if user and hasattr(user, 'tenant'):
                print(f"  User tenant: {user.tenant}")
                print(f"  User role: {getattr(user, 'role', 'NO_ROLE')}")
            print(f"  Content-Type: {request.headers.get('Content-Type')}")
            print(f"  Authorization: {request.META.get('HTTP_AUTHORIZATION', 'None')[:50]}...")

        # Skip tenant processing for OPTIONS requests (CORS preflight)
        if request.method == 'OPTIONS':
            print(f"  ✅ Skipping OPTIONS request")
            return super().process_request(request)

        # Skip tenant processing for public endpoints that should use public schema
        public_paths = ['/admin/', '/swagger/', '/redoc/', '/static/', '/media/', '/api/auth/', '/api/shared/', '/api/tenants/get-tenant-data/', '/api/idcards/services/public/', '/api/tenants/system-settings/', '/api/kebele-dashboard/', '/api/dashboard-test/']

        # Debug logging for get-tenant-data endpoint
        if '/api/tenants/get-tenant-data/' in request.path:
            print(f"🔍 MIDDLEWARE DEBUG: get-tenant-data endpoint accessed")
            print(f"  Path: {request.path}")
            print(f"  Method: {request.method}")
            print(f"  Is public path: {any(request.path.startswith(path) for path in public_paths)}")
            print(f"  User: {getattr(request, 'user', 'NO_USER')}")
            print(f"  Authorization header: {'HTTP_AUTHORIZATION' in request.META}")

        # Also skip for tenant management endpoints (not tenant-specific data)
        tenant_management_patterns = [
            '/api/tenants/$',  # Exact match for tenant list
            '/api/tenants/\\d+/$',  # Tenant detail (CRUD operations)
            '/api/tenants/\\d+/users/$',  # Tenant users management
            '/api/tenants/\\d+/create_user/$',  # Tenant create user
            '/api/tenants/\\d+/update_user/$',  # Tenant update user
            '/api/tenants/\\d+/change_user_password/$',  # Tenant change user password
            '/api/tenants/\\d+/create_admin/$',  # Tenant create admin
            '/api/tenants/subcities/',  # Subcities list
            '/api/tenants/kebeles/',  # Kebeles list
            '/api/tenants/ketenas/',  # Ketenas list
            '/api/tenants/cities/',  # Cities list
            '/api/tenants/domains/',  # Domains list
            '/api/tenants/registration/',  # Tenant registration
            '/api/tenants/switch/',  # Tenant switching
        ]

        # Tenant-specific endpoints that use user's tenant context (not URL-based tenant ID)
        tenant_context_patterns = [
            '/api/tenants/transfers/',  # Transfer endpoints use user's tenant context
            '/api/tenants/citizen-transfer-request/',  # Citizen transfer request endpoint
            '/api/tenants/transfer-citizen-request/',  # New transfer endpoint for testing
            '/api/tenants/clearances/',  # Clearance endpoints use user's tenant context
            '/api/reports/',  # Reports endpoints use user's tenant context
        ]

        # Special handling for transfer endpoints - COMPLETELY bypass django-tenants
        if (request.path.startswith('/api/tenants/transfers/') or
            request.path.startswith('/api/tenants/citizen-transfer-request/') or
            request.path.startswith('/api/tenants/transfer-citizen-request/') or
            request.path.startswith('/api/tenants/citizen-transfer-test/')):
            print(f"🔍 MIDDLEWARE: Transfer endpoint detected - BYPASSING django-tenants completely")
            print(f"  Path: {request.path}")
            print(f"  Method: {request.method}")

            # Extract tenant ID from JWT token
            tenant_id = self._get_tenant_from_jwt(request)
            print(f"  Extracted tenant_id from JWT: {tenant_id}")

            if tenant_id:
                try:
                    TenantModel = get_tenant_model()
                    tenant = TenantModel.objects.get(id=tenant_id)

                    # Set tenant context directly without domain requirement
                    request.tenant = tenant
                    connection = connections['default']
                    connection.set_tenant(request.tenant)
                    print(f"  ✅ Tenant context set: {tenant.name}")

                    # CRITICAL: Return None to prevent django-tenants from running
                    return None

                except (TenantModel.DoesNotExist, DomainModel.DoesNotExist, ValueError) as e:
                    print(f"  ❌ Error setting tenant context: {e}")

            print(f"  ❌ Could not set tenant context, using public schema")
            connection = connections['default']
            connection.set_schema_to_public()

            # CRITICAL: Return None to prevent django-tenants from running
            return None

        # Check public paths first
        is_public_path = any(request.path.startswith(path) for path in public_paths)
        print(f"🔍 MIDDLEWARE: Public path check - Path: {request.path}, Is public: {is_public_path}")

        if is_public_path:
            print(f"✅ MIDDLEWARE: Using public schema for public path: {request.path}")
            # For public paths, ensure we're using the public schema
            connection = connections['default']
            connection.set_schema_to_public()
            return None

        # Check tenant management patterns
        try:
            for pattern in tenant_management_patterns:
                if re.match(pattern, request.path):
                    # For tenant management endpoints, use public schema
                    connection = connections['default']
                    connection.set_schema_to_public()
                    return None
        except Exception as e:
            # If regex fails, log and continue
            print(f"Middleware regex error: {e}")
            pass

        # Check tenant context patterns (use user's tenant from JWT)
        try:
            for pattern in tenant_context_patterns:
                if request.path.startswith(pattern):
                    print(f"🔍 MIDDLEWARE: Transfer endpoint detected: {request.path}")
                    print(f"  Method: {request.method}")
                    print(f"  Pattern matched: {pattern}")

                    # These endpoints use the user's tenant context from JWT
                    # We need to set tenant context from the JWT token
                    tenant_id = self._get_tenant_from_jwt(request)
                    print(f"  Extracted tenant_id from JWT: {tenant_id}")

                    if tenant_id:
                        try:
                            TenantModel = get_tenant_model()
                            tenant = TenantModel.objects.get(id=tenant_id)

                            DomainModel = get_tenant_domain_model()
                            domain = DomainModel.objects.filter(tenant=tenant).first()

                            # Set tenant context even without domain (for IP-based access)
                            request.tenant = tenant
                            if domain:
                                request.domain = domain
                                print(f"  ✅ Tenant context set with domain: {tenant.name} -> {domain.domain}")
                            else:
                                print(f"  ✅ Tenant context set without domain: {tenant.name} (IP-based access)")

                            connection = connections['default']
                            connection.set_tenant(request.tenant)
                            # CRITICAL: Return None to prevent django-tenants from creating FakeTenant
                            return None
                        except (TenantModel.DoesNotExist, DomainModel.DoesNotExist, ValueError) as e:
                            print(f"  ❌ Error setting tenant context: {e}")
                            pass
                    else:
                        print(f"  ❌ No tenant_id extracted from JWT")
                    return None
        except Exception as e:
            print(f"Middleware tenant context error: {e}")
            pass

        # Handle tenant-specific API requests
        if request.path.startswith('/api/tenants/'):
            # Extract tenant ID from URL pattern like /api/tenants/42/idcards/
            tenant_match = re.match(r'/api/tenants/(\d+)/', request.path)
            if tenant_match:
                tenant_id = tenant_match.group(1)
                try:
                    # Get the tenant model
                    TenantModel = get_tenant_model()
                    tenant = TenantModel.objects.get(id=tenant_id)

                    # Get a domain for this tenant
                    DomainModel = get_tenant_domain_model()
                    domain = DomainModel.objects.filter(tenant=tenant).first()

                    if domain:
                        request.tenant = tenant
                        request.domain = domain
                        connection = connections['default']
                        connection.set_tenant(request.tenant)
                        return None
                except (TenantModel.DoesNotExist, DomainModel.DoesNotExist, ValueError):
                    return JsonResponse({"detail": "Invalid tenant ID"}, status=400)

        # Get the tenant from the request header if present (for other API requests)
        tenant_id = request.headers.get('X-Tenant-ID')

        if tenant_id and request.path.startswith('/api/'):
            try:
                # Get the tenant model
                TenantModel = get_tenant_model()
                tenant = TenantModel.objects.get(id=tenant_id)

                # Get a domain for this tenant
                DomainModel = get_tenant_domain_model()
                domain = DomainModel.objects.filter(tenant=tenant).first()

                if domain:
                    request.tenant = tenant
                    request.domain = domain
                    connection = connections['default']
                    connection.set_tenant(request.tenant)
                    return None
            except (TenantModel.DoesNotExist, DomainModel.DoesNotExist, ValueError):
                return JsonResponse({"detail": "Invalid tenant ID"}, status=400)

        # If no tenant header or not an API request, use the default behavior
        return super().process_request(request)

    def _get_tenant_from_jwt(self, request):
        """Extract tenant ID from JWT token."""
        try:
            auth_header = request.META.get('HTTP_AUTHORIZATION', '')
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]

                import jwt
                # Decode without verification to get tenant_id
                decoded = jwt.decode(token, options={"verify_signature": False})
                return decoded.get('tenant_id')
        except Exception as e:
            print(f"Error extracting tenant from JWT: {e}")
            return None
