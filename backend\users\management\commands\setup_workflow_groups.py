"""
Management command to set up workflow-specific groups for tenants.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from django_tenants.utils import schema_context, get_public_schema_name
from tenants.models import Tenant
from tenants.models_workflow import TenantWorkflowConfig
from users.services.workflow_group_manager import WorkflowGroupManager
from users.utils.group_permissions import create_management_group
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Set up workflow-specific groups for tenants'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-id',
            type=int,
            help='Specific tenant ID to set up (optional)',
        )
        parser.add_argument(
            '--tenant-type',
            choices=['kebele', 'subcity', 'city'],
            help='Set up groups for specific tenant type',
        )
        parser.add_argument(
            '--workflow-type',
            choices=['centralized', 'autonomous'],
            help='Set up groups for specific workflow type',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of existing groups',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 Setting up workflow-specific groups...'))
        
        # Get tenants to process
        tenants = self.get_tenants(options)
        
        if not tenants:
            self.stdout.write(self.style.WARNING('No tenants found matching criteria'))
            return
        
        self.stdout.write(f'Found {len(tenants)} tenants to process')
        
        total_groups_created = 0
        total_errors = 0
        
        for tenant in tenants:
            try:
                groups_created = self.setup_tenant_workflow_groups(tenant, options)
                total_groups_created += groups_created
                
                if groups_created > 0:
                    self.stdout.write(
                        self.style.SUCCESS(f'✅ {tenant.name}: Created {groups_created} groups')
                    )
                else:
                    self.stdout.write(f'ℹ️ {tenant.name}: No groups needed')
                    
            except Exception as e:
                total_errors += 1
                self.stdout.write(
                    self.style.ERROR(f'❌ {tenant.name}: Error - {e}')
                )
                logger.error(f"Error setting up groups for {tenant.name}: {e}")
        
        # Summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS(f'✅ Setup completed!'))
        self.stdout.write(f'📊 Total groups created: {total_groups_created}')
        self.stdout.write(f'❌ Errors encountered: {total_errors}')
        
        if options['dry_run']:
            self.stdout.write(self.style.WARNING('🔍 This was a dry run - no changes were made'))

    def get_tenants(self, options):
        """Get tenants based on command options."""
        queryset = Tenant.objects.all()
        
        if options['tenant_id']:
            queryset = queryset.filter(id=options['tenant_id'])
        elif options['tenant_type']:
            queryset = queryset.filter(type=options['tenant_type'])
        
        return list(queryset)

    def setup_tenant_workflow_groups(self, tenant, options):
        """Set up workflow groups for a specific tenant."""
        groups_created = 0
        
        # Get or determine workflow type
        workflow_type = options.get('workflow_type')
        if not workflow_type:
            try:
                workflow_config = TenantWorkflowConfig.objects.get(tenant=tenant)
                workflow_type = workflow_config.workflow_type
            except TenantWorkflowConfig.DoesNotExist:
                workflow_type = 'centralized'  # Default
        
        self.stdout.write(f'🔧 Setting up {workflow_type} workflow groups for {tenant.name}')
        
        # Define required groups based on workflow and tenant type
        required_groups = self.get_required_groups(tenant, workflow_type)
        
        if options['dry_run']:
            self.stdout.write(f'   Would create {len(required_groups)} groups:')
            for group_template in required_groups:
                group_name = f"{tenant.name}_{group_template}"
                self.stdout.write(f'     - {group_name}')
            return len(required_groups)
        
        # Create groups
        with schema_context(get_public_schema_name()):
            for group_template in required_groups:
                try:
                    group_name = f"{tenant.name}_{group_template}"
                    
                    # Check if group already exists
                    from django.contrib.auth.models import Group
                    if Group.objects.filter(name=group_name).exists():
                        if options['force']:
                            Group.objects.filter(name=group_name).delete()
                            self.stdout.write(f'   🗑️ Deleted existing group: {group_name}')
                        else:
                            self.stdout.write(f'   ⏭️ Skipping existing group: {group_name}')
                            continue
                    
                    # Create the group
                    success, result, tenant_group = create_management_group(
                        group_name=group_name,
                        permission_set_name=group_template,
                        tenant=tenant,
                        description=f"{group_template.replace('_', ' ').title()} for {tenant.name} ({workflow_type} workflow)"
                    )
                    
                    if success:
                        groups_created += 1
                        self.stdout.write(f'   ✅ Created: {group_name}')
                    else:
                        self.stdout.write(f'   ❌ Failed to create {group_name}: {result}')
                        
                except Exception as e:
                    self.stdout.write(f'   ❌ Error creating {group_template}: {e}')
        
        return groups_created

    def get_required_groups(self, tenant, workflow_type):
        """Get required group templates based on tenant type and workflow."""
        required_groups = []
        
        if tenant.type == 'kebele':
            if workflow_type == 'autonomous':
                required_groups = [
                    'autonomous_clerk',
                    'autonomous_kebele_leader',
                    'designated_printer'
                ]
            else:  # centralized
                required_groups = [
                    'centralized_clerk',
                    'centralized_kebele_leader'
                ]
        
        elif tenant.type == 'subcity':
            required_groups = [
                'subcity_management'
            ]
        
        elif tenant.type == 'city':
            required_groups = [
                'city_management'
            ]
        
        return required_groups

    def setup_workflow_configuration(self, tenant, workflow_type):
        """Ensure tenant has proper workflow configuration."""
        try:
            workflow_config, created = TenantWorkflowConfig.objects.get_or_create(
                tenant=tenant,
                defaults={'workflow_type': workflow_type}
            )
            
            if created:
                self.stdout.write(f'   📋 Created workflow config: {workflow_type}')
            elif workflow_config.workflow_type != workflow_type:
                workflow_config.workflow_type = workflow_type
                workflow_config.save()
                self.stdout.write(f'   📋 Updated workflow config: {workflow_type}')
            
            return workflow_config
            
        except Exception as e:
            self.stdout.write(f'   ❌ Error setting up workflow config: {e}')
            return None

    def assign_users_to_workflow_groups(self, tenant, workflow_type, options):
        """Assign existing users to appropriate workflow groups."""
        if options['dry_run']:
            return 0
        
        try:
            workflow_manager = WorkflowGroupManager(tenant)
            
            # Get current workflow and switch if needed
            current_workflow = workflow_manager.workflow_config.workflow_type
            if current_workflow != workflow_type:
                result = workflow_manager.switch_workflow(
                    new_workflow_type=workflow_type,
                    switched_by=None,
                    reason="Initial workflow setup"
                )
                
                if result['success']:
                    self.stdout.write(f'   👥 Assigned {result["affected_users"]} users to workflow groups')
                    return result['affected_users']
                else:
                    self.stdout.write(f'   ❌ Failed to assign users: {result["message"]}')
                    return 0
            
            return 0
            
        except Exception as e:
            self.stdout.write(f'   ❌ Error assigning users: {e}')
            return 0
