"""
Management command to set up subcity admin groups and assign users properly.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django_tenants.utils import schema_context, get_public_schema_name
from users.models import User
from users.models_groups import TenantGroup, GroupMembership
from tenants.models import Tenant
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Set up subcity admin groups and assign users properly'

    def add_arguments(self, parser):
        parser.add_argument(
            '--subcity-id',
            type=int,
            help='Specific subcity ID to set up (optional)',
        )
        parser.add_argument(
            '--user-email',
            type=str,
            help='Specific user email to verify after setup',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 Setting up subcity admin groups...'))
        self.stdout.write('='*60)
        
        try:
            # Step 1: Create required permissions
            self.create_required_permissions(options['dry_run'])
            
            # Step 2: Create subcity admin group template
            group = self.create_subcity_admin_group(options['dry_run'])
            
            # Step 3: Set up subcity-specific groups
            if options['subcity_id']:
                subcities = [Tenant.objects.get(id=options['subcity_id'], type='subcity')]
            else:
                subcities = Tenant.objects.filter(type='subcity')
            
            self.setup_subcity_groups(subcities, options['dry_run'])
            
            # Step 4: Assign existing subcity admin users
            self.assign_subcity_users_to_groups(subcities, options['dry_run'])
            
            # Step 5: Verify specific user if provided
            if options['user_email']:
                self.verify_user_permissions(options['user_email'])
            
            self.stdout.write('')
            self.stdout.write('='*60)
            if options['dry_run']:
                self.stdout.write(self.style.WARNING('🔍 This was a dry run - no changes were made'))
            else:
                self.stdout.write(self.style.SUCCESS('✅ Subcity admin group setup completed!'))
            
            self.stdout.write('')
            self.stdout.write('Next steps:')
            self.stdout.write('1. Subcity admin users need to log out and log back in')
            self.stdout.write('2. Test access to Citizens, ID Cards, and User Management pages')
            self.stdout.write('3. Verify group assignments with: --user-email <email>')
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error during setup: {e}'))
            import traceback
            traceback.print_exc()

    def create_required_permissions(self, dry_run=False):
        """Create the required permissions if they don't exist."""
        self.stdout.write('🔧 Creating required permissions...')
        
        # Get User content type
        user_content_type = ContentType.objects.get_for_model(User)
        
        required_permissions = [
            ('manage_users', 'Can manage users'),
            ('manage_citizens', 'Can manage citizens'),
            ('manage_idcards', 'Can manage ID cards'),
            ('view_citizens', 'Can view citizens'),
            ('view_idcards', 'Can view ID cards'),
            ('view_reports', 'Can view reports'),
            ('view_child_kebeles_data', 'Can view child kebeles data'),
            ('create_kebele_users', 'Can create kebele users'),
            ('print_id_cards', 'Can print ID cards'),
            ('approve_id_cards', 'Can approve ID cards'),
            ('navigate_to_dashboard', 'Can navigate to dashboard'),
            ('view_subcity_dashboard', 'Can view subcity dashboard'),
            ('navigate_to_citizens', 'Can navigate to citizens'),
            ('navigate_to_id_cards', 'Can navigate to ID cards'),
            ('view_user_management', 'Can view user management'),
            ('view_subcity_management', 'Can view subcity management'),
            ('view_kebele_management', 'Can view kebele management'),
        ]
        
        created_count = 0
        for codename, name in required_permissions:
            if dry_run:
                exists = Permission.objects.filter(codename=codename, content_type=user_content_type).exists()
                if not exists:
                    self.stdout.write(f'  Would create permission: {codename}')
                    created_count += 1
                else:
                    self.stdout.write(f'  Permission already exists: {codename}')
            else:
                permission, created = Permission.objects.get_or_create(
                    codename=codename,
                    content_type=user_content_type,
                    defaults={'name': name}
                )
                if created:
                    self.stdout.write(f'  ✅ Created permission: {codename}')
                    created_count += 1
                else:
                    self.stdout.write(f'  ℹ️ Permission already exists: {codename}')
        
        self.stdout.write(f'📊 {"Would create" if dry_run else "Created"} {created_count} permissions')

    def create_subcity_admin_group(self, dry_run=False):
        """Create or update the main subcity admin group template."""
        self.stdout.write('🏗️ Creating subcity admin group template...')
        
        with schema_context(get_public_schema_name()):
            group_name = "SubCity Administrators"
            
            if dry_run:
                exists = Group.objects.filter(name=group_name).exists()
                if not exists:
                    self.stdout.write(f'  Would create group: {group_name}')
                else:
                    self.stdout.write(f'  Group already exists: {group_name}')
                return None
            
            # Create or get the subcity admin group
            group, created = Group.objects.get_or_create(name=group_name)
            
            if created:
                self.stdout.write(f'  ✅ Created group: {group_name}')
            else:
                self.stdout.write(f'  ℹ️ Using existing group: {group_name}')
            
            # Define required permissions for subcity admin
            required_permission_codenames = [
                'manage_users', 'manage_citizens', 'manage_idcards',
                'view_citizens', 'view_idcards', 'view_reports',
                'view_child_kebeles_data', 'create_kebele_users',
                'print_id_cards', 'approve_id_cards',
                'navigate_to_dashboard', 'view_subcity_dashboard',
                'navigate_to_citizens', 'navigate_to_id_cards',
                'view_user_management', 'view_subcity_management', 'view_kebele_management',
            ]
            
            # Get existing permissions
            existing_permissions = set(group.permissions.values_list('codename', flat=True))
            
            # Add missing permissions
            added_count = 0
            for codename in required_permission_codenames:
                if codename not in existing_permissions:
                    try:
                        permission = Permission.objects.get(codename=codename)
                        group.permissions.add(permission)
                        self.stdout.write(f'  ➕ Added permission: {codename}')
                        added_count += 1
                    except Permission.DoesNotExist:
                        self.stdout.write(f'  ⚠️ Permission not found: {codename}')
            
            self.stdout.write(f'📊 Added {added_count} permissions to {group_name}')
            
            # Create TenantGroup if it doesn't exist
            tenant_group, created = TenantGroup.objects.get_or_create(
                group=group,
                defaults={
                    'tenant': None,  # Global group
                    'level': 20,  # Subcity level
                    'is_active': True
                }
            )
            
            if created:
                self.stdout.write(f'  ✅ Created TenantGroup for {group_name}')
            
            return group

    def setup_subcity_groups(self, subcities, dry_run=False):
        """Set up subcity-specific groups."""
        self.stdout.write(f'🏢 Setting up groups for {len(subcities)} subcities...')
        
        for subcity in subcities:
            self.stdout.write(f'  Processing subcity: {subcity.name}')
            
            if dry_run:
                self.stdout.write(f'    Would set up subcity-specific group for {subcity.name}')
                continue
            
            # Create subcity-specific group name
            subcity_group_name = f"{subcity.name}_subcity_management"
            
            with schema_context(get_public_schema_name()):
                # Create subcity-specific group
                subcity_group, created = Group.objects.get_or_create(
                    name=subcity_group_name,
                    defaults={}
                )
                
                if created:
                    self.stdout.write(f'    ✅ Created subcity group: {subcity_group_name}')
                    
                    # Copy permissions from main subcity admin group
                    main_group = Group.objects.get(name="SubCity Administrators")
                    subcity_group.permissions.set(main_group.permissions.all())
                    
                    # Create TenantGroup
                    TenantGroup.objects.get_or_create(
                        group=subcity_group,
                        defaults={
                            'tenant': subcity,
                            'level': 20,
                            'is_active': True
                        }
                    )
                else:
                    self.stdout.write(f'    ℹ️ Subcity group already exists: {subcity_group_name}')

    def assign_subcity_users_to_groups(self, subcities, dry_run=False):
        """Assign subcity admin users to their appropriate groups."""
        self.stdout.write('👥 Assigning subcity admin users to groups...')
        
        total_assigned = 0
        
        for subcity in subcities:
            self.stdout.write(f'  Processing users in subcity: {subcity.name}')
            
            # Find subcity admin users in this subcity
            subcity_users = []
            
            try:
                with schema_context(subcity.schema_name):
                    users = User.objects.filter(role='subcity_admin', is_active=True)
                    subcity_users.extend([(user, subcity) for user in users])
            except Exception as e:
                self.stdout.write(f'    ⚠️ Error checking subcity {subcity.name}: {e}')
                continue
            
            if not subcity_users:
                self.stdout.write(f'    ℹ️ No subcity admin users found in {subcity.name}')
                continue
            
            self.stdout.write(f'    Found {len(subcity_users)} subcity admin users')
            
            if dry_run:
                for user, _ in subcity_users:
                    self.stdout.write(f'      Would assign {user.email} to groups')
                continue
            
            # Assign users to groups
            with schema_context(get_public_schema_name()):
                # Get both main and subcity-specific groups
                main_group = Group.objects.get(name="SubCity Administrators")
                subcity_group_name = f"{subcity.name}_subcity_management"
                
                try:
                    subcity_group = Group.objects.get(name=subcity_group_name)
                except Group.DoesNotExist:
                    subcity_group = None
                
                for user, user_subcity in subcity_users:
                    try:
                        # Add to main subcity admin group
                        main_group.user_set.add(user)
                        
                        # Create GroupMembership for main group
                        membership, created = GroupMembership.objects.get_or_create(
                            user_email=user.email,
                            group=TenantGroup.objects.get(group=main_group),
                            defaults={
                                'user_tenant_id': user_subcity.id,
                                'user_tenant_schema': user_subcity.schema_name,
                                'is_primary': True,
                                'assigned_by_email': 'system',
                                'reason': 'Automatic assignment for subcity admin role'
                            }
                        )
                        
                        if created:
                            self.stdout.write(f'      ✅ Assigned {user.email} to main group')
                            total_assigned += 1
                        
                        # Add to subcity-specific group if it exists
                        if subcity_group:
                            subcity_group.user_set.add(user)
                            self.stdout.write(f'      ✅ Assigned {user.email} to {subcity_group_name}')
                        
                    except Exception as e:
                        self.stdout.write(f'      ❌ Error assigning {user.email}: {e}')
        
        self.stdout.write(f'📊 Assigned {total_assigned} users to groups')

    def verify_user_permissions(self, user_email):
        """Verify that a specific user has the required permissions."""
        self.stdout.write(f'🔍 Verifying permissions for {user_email}...')
        
        try:
            # Find user across all schemas
            user = None
            user_schema = None
            
            # Check public schema first
            with schema_context(get_public_schema_name()):
                try:
                    user = User.objects.get(email=user_email)
                    user_schema = 'public'
                    self.stdout.write(f'  Found user in public schema')
                except User.DoesNotExist:
                    pass
            
            # Check tenant schemas
            if not user:
                for tenant in Tenant.objects.all():
                    try:
                        with schema_context(tenant.schema_name):
                            user = User.objects.get(email=user_email)
                            user_schema = tenant.schema_name
                            self.stdout.write(f'  Found user in {tenant.name} schema')
                            break
                    except User.DoesNotExist:
                        continue
            
            if not user:
                self.stdout.write(f'  ❌ User {user_email} not found')
                return False
            
            self.stdout.write(f'  User role: {user.role}')
            self.stdout.write(f'  User tenant: {user.tenant.name if user.tenant else "None"}')
            self.stdout.write(f'  User schema: {user_schema}')
            
            # Check group memberships
            with schema_context(get_public_schema_name()):
                user_groups = user.groups.all()
                self.stdout.write(f'  User groups: {[g.name for g in user_groups]}')
                
                # Check GroupMembership records
                memberships = GroupMembership.objects.filter(user_email=user_email, is_active=True)
                self.stdout.write(f'  Group memberships: {memberships.count()}')
                for membership in memberships:
                    self.stdout.write(f'    - {membership.group.group.name} (primary: {membership.is_primary})')
                
                # Check specific permissions
                required_permissions = ['manage_users', 'manage_citizens', 'manage_idcards']
                for perm in required_permissions:
                    has_perm = user.has_group_permission(perm)
                    status = "✅" if has_perm else "❌"
                    self.stdout.write(f'  {status} {perm}: {has_perm}')
            
            return True
            
        except Exception as e:
            self.stdout.write(f'  ❌ Error verifying user: {e}')
            return False
