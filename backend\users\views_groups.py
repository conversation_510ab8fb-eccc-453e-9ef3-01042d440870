from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q, Count, Prefetch
from django.contrib.auth.models import Group, Permission
from django.contrib.auth import get_user_model
from .models_groups import TenantGroup, GroupMembership, GroupTemplate
from .serializers_groups import (
    TenantGroupSerializer, GroupMembershipSerializer, GroupTemplateSerializer,
    GroupManagementSerializer, PermissionSerializer
)
from .permissions_tenant_roles import (
    get_manageable_tenants_for_user, get_users_in_manageable_tenants
)
from tenants.models import Tenant
from tenants.models_workflow import TenantWorkflowConfig

User = get_user_model()


class GroupManagementViewSet(viewsets.ViewSet):
    """
    ViewSet for managing groups and group memberships.
    """
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def tenant_groups(self, request):
        """
        Get predefined roles for specific tenant types.
        Returns only the appropriate predefined roles based on tenant type.
        """
        tenant_id = request.query_params.get('tenant_id')

        # Get manageable tenants
        manageable_tenants = get_manageable_tenants_for_user(request.user)
        manageable_tenant_ids = [t.id for t in manageable_tenants]

        # If filtering by specific tenant, return only predefined roles for that tenant type
        if tenant_id:
            try:
                tenant = Tenant.objects.get(id=tenant_id)

                # Get workflow configuration for the tenant
                workflow_type = 'centralized'  # default
                if hasattr(tenant, 'workflow_config'):
                    workflow_type = tenant.workflow_config.workflow_type

                # Define predefined roles for each tenant type based on workflow
                if tenant.type == 'kebele':
                    # Base roles for kebele
                    allowed_roles = ['clerk', 'kebele_leader']

                    # Add print_id_cards role only for autonomous workflow
                    if workflow_type == 'autonomous':
                        allowed_roles.append('print_id_cards')
                        print(f"🔍 Kebele {tenant.name} is autonomous - adding print_id_cards role")
                    else:
                        print(f"🔍 Kebele {tenant.name} is centralized - print_id_cards role not available")

                elif tenant.type == 'subcity':
                    # Subcity always has print_id_cards for centralized printing
                    allowed_roles = ['subcity_admin', 'subcity_system_admin', 'print_id_cards']

                elif tenant.type == 'city':
                    # City level roles
                    allowed_roles = ['city_admin', 'city_system_admin']

                else:
                    allowed_roles = []

                # Filter to only include predefined roles for this tenant type
                queryset = TenantGroup.objects.filter(
                    Q(tenant__isnull=True) & Q(group__name__in=allowed_roles),
                    is_active=True
                ).select_related('tenant', 'created_by').prefetch_related('group__permissions')

            except Tenant.DoesNotExist:
                # Fallback to empty queryset if tenant not found
                queryset = TenantGroup.objects.none()
        else:
            # Base queryset - include tenant-specific groups and allowed global groups
            base_filter = Q(tenant_id__in=manageable_tenant_ids) | Q(tenant__isnull=True)

            queryset = TenantGroup.objects.filter(
                base_filter,
                is_active=True
            ).select_related('tenant', 'created_by').prefetch_related('group__permissions')
        
        # Serialize groups
        groups_data = []
        for group in queryset:
            group_data = {
                'id': group.id,
                'name': group.name,
                'description': group.description,
                'group_type': group.group_type,
                'level': group.level,
                'is_system_group': group.is_system_group,
                'tenant': {
                    'id': group.tenant.id if group.tenant else None,
                    'name': group.tenant.name if group.tenant else None,
                    'type': group.tenant.type if group.tenant else None
                } if group.tenant else None,
                'users_count': group.users_count,
                'permissions_count': group.permissions_count,
                'created_at': group.created_at,
                'created_by': group.created_by.email if group.created_by else None
            }
            groups_data.append(group_data)
        
        # Sort by level (highest first) then by name
        groups_data.sort(key=lambda x: (-x['level'], x['name']))
        
        return Response({
            'groups': groups_data,
            'total_groups': len(groups_data),
            'tenant_filter': tenant_id
        })

    @action(detail=False, methods=['post'])
    def setup_predefined_roles(self, request):
        """
        Set up predefined roles for the RBAC system.
        """
        from django.contrib.auth.models import Group
        from django_tenants.utils import schema_context, get_public_schema_name

        # Define roles with their permissions and allowed tenant types
        roles_config = {
            'clerk': {
                'description': 'Clerk role for kebele level tenants',
                'tenant_types': ['kebele'],
                'level': 10,
                'group_type': 'operational',
                'permissions': [
                    # Navigation permissions
                    'navigate_to_dashboard', 'navigate_to_citizens', 'navigate_to_id_cards',
                    # Dashboard access
                    'view_kebele_dashboard',
                    # Citizens management
                    'register_citizens', 'view_citizens_list', 'view_citizen_details',
                    # ID Cards
                    'generate_id_cards', 'view_id_cards_list',
                    # Reports
                    'view_kebele_reports', 'view_own_kebele_data'
                ]
            },
            'kebele_leader': {
                'description': 'Kebele leader role for kebele level tenants',
                'tenant_types': ['kebele'],
                'level': 20,
                'group_type': 'administrative',
                'permissions': [
                    # Navigation permissions
                    'navigate_to_dashboard', 'navigate_to_citizens', 'navigate_to_id_cards',
                    # Dashboard access
                    'view_kebele_dashboard',
                    # Citizens management
                    'view_citizens_list', 'view_citizen_details',
                    # ID Cards
                    'view_id_cards_list', 'approve_id_cards', 'verify_documents',
                    # User management
                    'view_user_management',
                    # Reports
                    'view_kebele_reports', 'view_own_kebele_data',
                    # Transfer and Clearance workflows
                    'create_transfers', 'approve_transfer_requests', 'view_transfers',
                    'create_clearances', 'view_clearances', 'manage_clearances'
                ]
            },
            'subcity_admin': {
                'description': 'Subcity admin role for subcity level tenants',
                'tenant_types': ['subcity'],
                'level': 30,
                'group_type': 'administrative',
                'permissions': [
                    # Navigation permissions
                    'navigate_to_dashboard', 'navigate_to_citizens', 'navigate_to_id_cards',
                    # Dashboard access
                    'view_subcity_dashboard',
                    # Citizens management
                    'view_child_kebeles_data', 'view_citizens_list', 'view_citizen_details',
                    # ID Cards
                    'approve_id_cards', 'print_id_cards', 'view_id_cards_list', 'send_id_cards_to_higher_level',
                    # User management
                    'create_kebele_users', 'view_user_management',
                    # Reports
                    'view_subcity_reports', 'verify_documents'
                ]
            },
            'subcity_system_admin': {
                'description': 'Subcity system admin role for managing kebele users',
                'tenant_types': ['subcity'],
                'level': 35,
                'group_type': 'administrative',
                'permissions': [
                    # Navigation permissions
                    'navigate_to_dashboard',
                    # Dashboard access
                    'view_subcity_dashboard',
                    # User management
                    'create_kebele_users', 'view_user_management'
                ]
            },
            'city_admin': {
                'description': 'City admin role for city level tenants',
                'tenant_types': ['city'],
                'level': 40,
                'group_type': 'administrative',
                'permissions': [
                    # Navigation permissions
                    'navigate_to_dashboard', 'navigate_to_citizens',
                    # Dashboard access
                    'view_city_dashboard',
                    # Citizens management
                    'view_child_subcities_data', 'view_citizens_list', 'view_citizen_details',
                    # User management
                    'create_subcity_users', 'view_user_management',
                    # Reports
                    'view_city_reports', 'view_all_reports', 'manage_tenants'
                ]
            },
            'city_system_admin': {
                'description': 'City system admin role for managing subcity users',
                'tenant_types': ['city'],
                'level': 45,
                'group_type': 'administrative',
                'permissions': [
                    # Navigation permissions
                    'navigate_to_dashboard',
                    # Dashboard access
                    'view_city_dashboard',
                    # User management
                    'create_subcity_users', 'view_user_management'
                ]
            },
            'print_id_cards': {
                'description': 'ID card printing role for autonomous kebeles or centralized subcities',
                'tenant_types': ['kebele', 'subcity'],
                'level': 15,
                'group_type': 'operational',
                'permissions': [
                    # Navigation permissions
                    'navigate_to_id_cards',
                    # ID Cards
                    'print_id_cards', 'view_id_cards_list'
                ]
            }
        }

        created_count = 0
        updated_count = 0

        with schema_context(get_public_schema_name()):
            for role_name, config in roles_config.items():
                # Create Django group
                group, created = Group.objects.get_or_create(name=role_name)

                if created:
                    created_count += 1

                # Add permissions to group
                if 'permissions' in config:
                    from django.contrib.auth.models import Permission
                    permissions_added = 0
                    for perm_codename in config['permissions']:
                        try:
                            # Try to find permission with app label
                            if '.' in perm_codename:
                                app_label, codename = perm_codename.split('.', 1)
                                permission = Permission.objects.get(
                                    content_type__app_label=app_label,
                                    codename=codename
                                )
                            else:
                                # Try to find permission by codename only
                                permission = Permission.objects.get(codename=perm_codename)

                            if not group.permissions.filter(id=permission.id).exists():
                                group.permissions.add(permission)
                                permissions_added += 1
                        except Permission.DoesNotExist:
                            # Permission doesn't exist, skip it
                            pass

                # Create or update TenantGroup
                tenant_group, tg_created = TenantGroup.objects.get_or_create(
                    group=group,
                    defaults={
                        'name': role_name,
                        'description': config['description'],
                        'group_type': config['group_type'],
                        'level': config['level'],
                        'allowed_tenant_types': config['tenant_types'],
                        'is_active': True,
                        'tenant': None  # Global role
                    }
                )

                if not tg_created:
                    # Update existing TenantGroup
                    tenant_group.allowed_tenant_types = config['tenant_types']
                    tenant_group.level = config['level']
                    tenant_group.description = config['description']
                    tenant_group.group_type = config['group_type']
                    tenant_group.save()
                    updated_count += 1

        return Response({
            'message': 'Predefined roles setup completed',
            'created_groups': created_count,
            'updated_groups': updated_count,
            'total_roles': len(roles_config)
        })

    @action(detail=False, methods=['post'])
    def setup_navigation_permissions(self, request):
        """
        Set up navigation permissions for the RBAC system.
        """
        from django.contrib.auth.models import Permission
        from django.contrib.contenttypes.models import ContentType
        from django_tenants.utils import schema_context, get_public_schema_name

        with schema_context(get_public_schema_name()):
            # Get or create content type for users app
            content_type, _ = ContentType.objects.get_or_create(
                app_label='users',
                model='user'
            )

            # Define navigation permissions
            navigation_permissions = [
                # Basic navigation
                ('navigate_to_dashboard', 'Can navigate to dashboard'),
                ('navigate_to_citizens', 'Can navigate to citizens'),
                ('navigate_to_id_cards', 'Can navigate to ID cards'),
                ('navigate_to_reports', 'Can navigate to reports'),

                # Dashboard permissions
                ('view_kebele_dashboard', 'Can view kebele dashboard'),
                ('view_subcity_dashboard', 'Can view subcity dashboard'),
                ('view_city_dashboard', 'Can view city dashboard'),

                # Citizens permissions
                ('register_citizens', 'Can register citizens'),
                ('view_citizens_list', 'Can view citizens list'),
                ('view_citizen_details', 'Can view citizen details'),
                ('view_own_kebele_data', 'Can view own kebele data'),
                ('view_child_kebeles_data', 'Can view child kebeles data'),
                ('view_child_subcities_data', 'Can view child subcities data'),

                # ID Cards permissions
                ('generate_id_cards', 'Can generate ID cards'),
                ('view_id_cards_list', 'Can view ID cards list'),
                ('approve_id_cards', 'Can approve ID cards'),
                ('print_id_cards', 'Can print ID cards'),
                ('send_id_cards_to_higher_level', 'Can send ID cards to higher level'),
                ('verify_documents', 'Can verify documents'),

                # User management permissions
                ('view_user_management', 'Can view user management'),
                ('create_kebele_users', 'Can create kebele users'),
                ('create_subcity_users', 'Can create subcity users'),

                # Reports permissions
                ('view_kebele_reports', 'Can view kebele reports'),
                ('view_subcity_reports', 'Can view subcity reports'),
                ('view_city_reports', 'Can view city reports'),
                ('view_all_reports', 'Can view all reports'),

                # System permissions
                ('manage_tenants', 'Can manage tenants'),

                # Transfer and Clearance permissions
                ('create_transfers', 'Can create transfer requests'),
                ('approve_transfer_requests', 'Can approve transfer requests'),
                ('view_transfers', 'Can view transfers'),
                ('manage_transfers', 'Can manage transfers'),
                ('create_clearances', 'Can create clearance requests'),
                ('view_clearances', 'Can view clearances'),
                ('manage_clearances', 'Can manage clearances'),
                ('approve_clearance_requests', 'Can approve clearance requests'),
            ]

            created_count = 0
            for codename, name in navigation_permissions:
                permission, created = Permission.objects.get_or_create(
                    codename=codename,
                    content_type=content_type,
                    defaults={'name': name}
                )

                if created:
                    created_count += 1

        return Response({
            'message': 'Navigation permissions setup completed',
            'created_permissions': created_count,
            'total_permissions': len(navigation_permissions)
        })

    @action(detail=False, methods=['post'])
    def fix_predefined_roles_complete(self, request):
        """
        Complete fix for predefined roles system - start from scratch.
        """
        from django.contrib.auth.models import Permission, Group
        from django.contrib.contenttypes.models import ContentType
        from django_tenants.utils import schema_context, get_public_schema_name
        from users.models_groups import TenantGroup, GroupMembership
        from users.models import User
        from tenants.models import Tenant

        try:
            # Step 1: Create all permissions
            with schema_context(get_public_schema_name()):
                content_type, _ = ContentType.objects.get_or_create(
                    app_label='users',
                    model='user'
                )

                # Define all permissions needed
                all_permissions = [
                    # Basic navigation and dashboard
                    ('view_kebele_dashboard', 'Can view kebele dashboard'),
                    ('view_subcity_dashboard', 'Can view subcity dashboard'),
                    ('view_city_dashboard', 'Can view city dashboard'),

                    # Citizens permissions
                    ('register_citizens', 'Can register citizens'),
                    ('view_citizens_list', 'Can view citizens list'),
                    ('view_citizen_details', 'Can view citizen details'),
                    ('view_own_kebele_data', 'Can view own kebele data'),
                    ('view_child_kebeles_data', 'Can view child kebeles data'),
                    ('view_child_subcities_data', 'Can view child subcities data'),

                    # ID Cards permissions
                    ('generate_id_cards', 'Can generate ID cards'),
                    ('view_id_cards_list', 'Can view ID cards list'),
                    ('approve_id_cards', 'Can approve ID cards'),
                    ('print_id_cards', 'Can print ID cards'),
                    ('send_id_cards_to_higher_level', 'Can send ID cards to higher level'),
                    ('verify_documents', 'Can verify documents'),

                    # User management permissions
                    ('create_kebele_users', 'Can create kebele users'),
                    ('create_subcity_users', 'Can create subcity users'),

                    # Reports permissions
                    ('view_kebele_reports', 'Can view kebele reports'),
                    ('view_subcity_reports', 'Can view subcity reports'),
                    ('view_city_reports', 'Can view city reports'),
                    ('view_all_reports', 'Can view all reports'),

                    # Transfer and Clearance permissions
                    ('create_transfers', 'Can create transfer requests'),
                    ('approve_transfer_requests', 'Can approve transfer requests'),
                    ('create_clearances', 'Can create clearance requests'),
                    ('view_clearances', 'Can view clearances'),

                    # System permissions
                    ('manage_tenants', 'Can manage tenants'),
                ]

                created_permissions = 0
                for codename, name in all_permissions:
                    permission, created = Permission.objects.get_or_create(
                        codename=codename,
                        content_type=content_type,
                        defaults={'name': name}
                    )
                    if created:
                        created_permissions += 1

            # Step 2: Create predefined roles with exact permissions
            roles_config = {
                'clerk': {
                    'description': 'Clerk role for kebele level tenants',
                    'tenant_types': ['kebele'],
                    'level': 10,
                    'group_type': 'operational',
                    'permissions': [
                        'register_citizens', 'view_citizens_list', 'view_citizen_details',
                        'generate_id_cards', 'view_id_cards_list', 'view_kebele_dashboard'
                    ]
                },
                'kebele_leader': {
                    'description': 'Kebele leader role for kebele level tenants',
                    'tenant_types': ['kebele'],
                    'level': 20,
                    'group_type': 'administrative',
                    'permissions': [
                        'view_own_kebele_data', 'view_kebele_dashboard', 'view_kebele_reports',
                        'view_citizens_list', 'view_citizen_details', 'view_id_cards_list',
                        'approve_id_cards', 'verify_documents', 'create_transfers',
                        'approve_transfer_requests', 'create_clearances', 'view_clearances'
                    ]
                },
                'subcity_admin': {
                    'description': 'Subcity admin role for subcity level tenants',
                    'tenant_types': ['subcity'],
                    'level': 30,
                    'group_type': 'administrative',
                    'permissions': [
                        'view_child_kebeles_data', 'view_subcity_dashboard', 'view_subcity_reports',
                        'approve_id_cards', 'print_id_cards', 'send_id_cards_to_higher_level',
                        'verify_documents', 'create_kebele_users'
                    ]
                },
                'subcity_system_admin': {
                    'description': 'Subcity system admin for managing kebele users',
                    'tenant_types': ['subcity'],
                    'level': 35,
                    'group_type': 'administrative',
                    'permissions': ['create_kebele_users']
                },
                'city_admin': {
                    'description': 'City admin role for city level tenants',
                    'tenant_types': ['city'],
                    'level': 40,
                    'group_type': 'administrative',
                    'permissions': [
                        'view_child_subcities_data', 'view_city_dashboard', 'view_city_reports',
                        'manage_tenants', 'view_all_reports', 'create_subcity_users'
                    ]
                },
                'city_system_admin': {
                    'description': 'City system admin for managing subcity users',
                    'tenant_types': ['city'],
                    'level': 45,
                    'group_type': 'administrative',
                    'permissions': ['create_subcity_users']
                },
                'print_id_cards': {
                    'description': 'ID card printing role for autonomous kebeles or centralized subcities',
                    'tenant_types': ['kebele', 'subcity'],
                    'level': 15,
                    'group_type': 'operational',
                    'permissions': ['print_id_cards']
                }
            }

            with schema_context(get_public_schema_name()):
                created_groups = 0
                updated_groups = 0

                for role_name, config in roles_config.items():
                    # Create Django group
                    group, created = Group.objects.get_or_create(name=role_name)
                    if created:
                        created_groups += 1

                    # Clear existing permissions and add new ones
                    group.permissions.clear()
                    permissions_added = 0

                    for perm_codename in config['permissions']:
                        try:
                            permission = Permission.objects.get(codename=perm_codename)
                            group.permissions.add(permission)
                            permissions_added += 1
                        except Permission.DoesNotExist:
                            pass

                    # Create or update TenantGroup
                    tenant_group, tg_created = TenantGroup.objects.get_or_create(
                        group=group,
                        defaults={
                            'name': role_name,
                            'description': config['description'],
                            'group_type': config['group_type'],
                            'level': config['level'],
                            'allowed_tenant_types': config['tenant_types'],
                            'is_active': True,
                            'tenant': None
                        }
                    )

                    if not tg_created:
                        # Update existing TenantGroup
                        tenant_group.allowed_tenant_types = config['tenant_types']
                        tenant_group.level = config['level']
                        tenant_group.description = config['description']
                        tenant_group.group_type = config['group_type']
                        tenant_group.save()
                        updated_groups += 1

            # Step 3: Reassign existing users
            all_users = []

            with schema_context(get_public_schema_name()):
                tenants = Tenant.objects.all()

                for tenant in tenants:
                    with schema_context(tenant.schema_name):
                        users = User.objects.filter(role__isnull=False).select_related('tenant')
                        for user in users:
                            all_users.append((user, tenant))

            role_mapping = {
                'clerk': 'clerk',
                'kebele_leader': 'kebele_leader',
                'subcity_admin': 'subcity_admin',
                'subcity_system_admin': 'subcity_system_admin',
                'city_admin': 'city_admin',
                'city_system_admin': 'city_system_admin'
            }

            assigned_users = 0
            with schema_context(get_public_schema_name()):
                for user, user_tenant in all_users:
                    group_name = role_mapping.get(user.role)
                    if not group_name:
                        continue

                    try:
                        group = Group.objects.get(name=group_name)
                        tenant_group = TenantGroup.objects.get(group=group)

                        # Check tenant type compatibility
                        if user_tenant.type not in tenant_group.allowed_tenant_types:
                            continue

                        # Clear existing group memberships and add to correct group
                        user.groups.clear()
                        group.user_set.add(user)

                        # Update GroupMembership
                        GroupMembership.objects.filter(user_email=user.email).delete()
                        GroupMembership.objects.create(
                            user_email=user.email,
                            user_tenant_id=user_tenant.id,
                            user_tenant_schema=user_tenant.schema_name,
                            group=tenant_group,
                            assigned_by_email='system',
                            reason=f'Complete fix for role: {user.role}',
                            is_primary=True,
                            is_active=True
                        )

                        assigned_users += 1

                    except Exception:
                        continue

            return Response({
                'message': 'Complete predefined roles fix completed successfully',
                'created_permissions': created_permissions,
                'created_groups': created_groups,
                'updated_groups': updated_groups,
                'assigned_users': assigned_users,
                'total_roles': len(roles_config)
            })

        except Exception as e:
            return Response({
                'error': f'Failed to fix predefined roles: {str(e)}'
            }, status=500)

    @action(detail=False, methods=['get'])
    def groups_with_users(self, request):
        """
        Get all groups with their users.
        """
        tenant_id = request.query_params.get('tenant_id')
        
        # Get manageable tenants
        manageable_tenants = get_manageable_tenants_for_user(request.user)
        manageable_tenant_ids = [t.id for t in manageable_tenants]
        
        # Base queryset - include tenant-specific groups and allowed global groups
        base_filter = Q(tenant_id__in=manageable_tenant_ids) | Q(tenant__isnull=True)

        # If filtering by specific tenant, add tenant type restrictions for global groups
        if tenant_id:
            try:
                tenant = Tenant.objects.get(id=tenant_id)
                # Include tenant-specific groups and global groups allowed for this tenant type
                base_filter = (
                    Q(tenant_id=tenant_id) |
                    (Q(tenant__isnull=True) & (
                        Q(allowed_tenant_types=[]) |  # No restrictions
                        Q(allowed_tenant_types__contains=[tenant.type])  # Allowed for this tenant type
                    ))
                )
            except Tenant.DoesNotExist:
                pass

        queryset = TenantGroup.objects.filter(
            base_filter,
            is_active=True
        ).select_related('tenant').prefetch_related(
            'group__user_set',
            'group__permissions'
        )
        
        groups_data = []
        for group in queryset:
            # Get users in this group using GroupMembership model (handles cross-schema lookup)
            memberships = GroupMembership.objects.filter(group=group, is_active=True)

            users_list = []
            for membership in memberships:
                try:
                    user = membership.user  # This uses our custom property that handles schema context
                    if user and user.is_active:
                        # Filter by manageable tenants
                        if user.tenant and user.tenant.id not in manageable_tenant_ids:
                            continue

                        # Filter by specific tenant if requested
                        if tenant_id and user.tenant and str(user.tenant.id) != str(tenant_id):
                            continue

                        users_list.append({
                            'id': user.id,
                            'email': user.email,
                            'first_name': user.first_name,
                            'last_name': user.last_name,
                            'full_name': f"{user.first_name} {user.last_name}".strip(),
                            'username': user.username,
                            'tenant': {
                                'id': user.tenant.id if user.tenant else None,
                                'name': user.tenant.name if user.tenant else None,
                                'type': user.tenant.type if user.tenant else None
                            } if user.tenant else None,
                            'date_joined': user.date_joined,
                            'last_login': user.last_login,
                            'is_primary': membership.is_primary
                        })
                except Exception as e:
                    # Log the error but continue processing other users
                    print(f"Error getting user for membership {membership.user_email}: {e}")
                    continue
            
            # Get permissions
            permissions_list = []
            for perm in group.group.permissions.all():
                permissions_list.append({
                    'codename': perm.codename,
                    'name': perm.name,
                    'content_type': perm.content_type.model if perm.content_type else None
                })
            
            group_data = {
                'id': group.id,
                'name': group.name,
                'description': group.description,
                'group_type': group.group_type,
                'level': group.level,
                'is_system_group': group.is_system_group,
                'tenant': {
                    'id': group.tenant.id if group.tenant else None,
                    'name': group.tenant.name if group.tenant else None,
                    'type': group.tenant.type if group.tenant else None
                } if group.tenant else None,
                'users': users_list,
                'users_count': len(users_list),
                'permissions': permissions_list,
                'permissions_count': len(permissions_list),
                'created_at': group.created_at
            }
            groups_data.append(group_data)
        
        # Sort by level (highest first) then by name
        groups_data.sort(key=lambda x: (-x['level'], x['name']))
        
        return Response({
            'groups': groups_data,
            'total_groups': len(groups_data),
            'total_users': sum(group['users_count'] for group in groups_data),
            'tenant_filter': tenant_id
        })
    
    @action(detail=False, methods=['get'])
    def user_groups_details(self, request):
        """
        Get detailed user information with all groups and permissions.
        """
        user_id = request.query_params.get('user_id')
        user_email = request.query_params.get('user_email')
        
        if not user_id and not user_email:
            return Response(
                {'error': 'user_id or user_email parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            if user_id:
                user = User.objects.get(id=user_id, is_active=True)
            else:
                user = User.objects.get(email=user_email, is_active=True)
            
            # Check if current user can view this user's details
            manageable_users = get_users_in_manageable_tenants(request.user)
            if user not in manageable_users and not request.user.is_superuser:
                return Response(
                    {'error': 'You do not have permission to view this user\'s details'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # Get user's groups
            user_groups = user.get_user_groups()
            primary_group = user.get_primary_group()
            
            groups_info = []
            for group in user_groups:
                # Get membership details
                membership = GroupMembership.objects.filter(
                    user=user, group=group, is_active=True
                ).first()
                
                # Get group permissions
                permissions_list = []
                for perm in group.group.permissions.all():
                    permissions_list.append({
                        'codename': perm.codename,
                        'name': perm.name,
                        'content_type': perm.content_type.model if perm.content_type else None
                    })
                
                group_info = {
                    'id': group.id,
                    'name': group.name,
                    'description': group.description,
                    'group_type': group.group_type,
                    'level': group.level,
                    'is_primary': membership.is_primary if membership else False,
                    'is_system_group': group.is_system_group,
                    'assigned_at': membership.assigned_at if membership else None,
                    'assigned_by': membership.assigned_by.email if membership and membership.assigned_by else None,
                    'reason': membership.reason if membership else '',
                    'scope': group.tenant.name if group.tenant else 'Global',
                    'permissions': permissions_list,
                    'permissions_count': len(permissions_list)
                }
                groups_info.append(group_info)
            
            # Get effective permissions
            effective_permissions = user.get_effective_permissions()
            effective_permissions_list = []
            permissions_by_category = {}
            
            for perm in effective_permissions:
                perm_data = {
                    'codename': perm.codename,
                    'name': perm.name,
                    'content_type': perm.content_type.model if perm.content_type else None
                }
                effective_permissions_list.append(perm_data)
                
                # Group by content type (category)
                category = perm.content_type.model if perm.content_type else 'Other'
                if category not in permissions_by_category:
                    permissions_by_category[category] = []
                permissions_by_category[category].append(perm_data)
            
            return Response({
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'username': user.username,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'full_name': f"{user.first_name} {user.last_name}".strip(),
                    'is_active': user.is_active,
                    'is_staff': user.is_staff,
                    'is_superuser': user.is_superuser,
                    'date_joined': user.date_joined,
                    'last_login': user.last_login,
                    'tenant': {
                        'id': user.tenant.id if user.tenant else None,
                        'name': user.tenant.name if user.tenant else None,
                        'type': user.tenant.type if user.tenant else None,
                        'parent': user.tenant.parent.name if user.tenant and user.tenant.parent else None
                    } if user.tenant else None
                },
                'groups': {
                    'assigned_groups': groups_info,
                    'total_groups': len(groups_info),
                    'primary_group': {
                        'id': primary_group.id if primary_group else None,
                        'name': primary_group.name if primary_group else None,
                        'level': primary_group.level if primary_group else None
                    } if primary_group else None
                },
                'permissions': {
                    'effective_permissions': effective_permissions_list,
                    'total_permissions': len(effective_permissions_list),
                    'permissions_by_category': permissions_by_category
                }
            })
            
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=False, methods=['post'])
    def manage_group(self, request):
        """
        Manage groups (create, assign users, assign permissions, etc.)
        """
        serializer = GroupManagementSerializer(data=request.data, context={'request': request})
        
        if serializer.is_valid():
            try:
                result = serializer.save()
                return Response(result, status=status.HTTP_200_OK)
            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def permissions(self, request):
        """
        Get all available permissions for group assignment.
        """
        try:
            from django.contrib.auth.models import Permission
            from django_tenants.utils import schema_context, get_public_schema_name

            with schema_context(get_public_schema_name()):
                # Get all permissions, focusing on users app
                permissions = Permission.objects.select_related('content_type').filter(
                    content_type__app_label='users'
                ).values(
                    'id',
                    'codename',
                    'name',
                    'content_type__app_label'
                ).order_by('codename')

                permission_list = []
                for perm in permissions:
                    permission_list.append({
                        'id': perm['id'],
                        'codename': f"{perm['content_type__app_label']}.{perm['codename']}",
                        'name': perm['name'],
                        'content_type': perm['content_type__app_label']
                    })

                return Response({
                    'permissions': permission_list,
                    'count': len(permission_list)
                }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {'error': f'Failed to fetch permissions: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def group_permissions(self, request):
        """
        Get permissions for a specific group.
        """
        group_id = request.query_params.get('group_id')
        if not group_id:
            return Response(
                {'error': 'group_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from django_tenants.utils import schema_context, get_public_schema_name

            with schema_context(get_public_schema_name()):
                try:
                    tenant_group = TenantGroup.objects.get(id=group_id, is_active=True)
                    group = tenant_group.group

                    # Get group permissions
                    permissions = group.permissions.select_related('content_type').values(
                        'id',
                        'codename',
                        'name',
                        'content_type__app_label'
                    )

                    permission_list = []
                    for perm in permissions:
                        permission_list.append({
                            'id': perm['id'],
                            'codename': f"{perm['content_type__app_label']}.{perm['codename']}",
                            'name': perm['name'],
                            'content_type': perm['content_type__app_label']
                        })

                    return Response({
                        'group_id': group_id,
                        'group_name': group.name,
                        'permissions': permission_list,
                        'count': len(permission_list)
                    }, status=status.HTTP_200_OK)

                except TenantGroup.DoesNotExist:
                    return Response(
                        {'error': 'Group not found'},
                        status=status.HTTP_404_NOT_FOUND
                    )

        except Exception as e:
            return Response(
                {'error': f'Failed to fetch group permissions: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def available_permissions(self, request):
        """
        Get all available permissions for group assignment.
        """
        permissions = Permission.objects.all().select_related('content_type')
        
        permissions_data = []
        permissions_by_category = {}
        
        for perm in permissions:
            category = perm.content_type.model if perm.content_type else 'Other'
            perm_data = {
                'id': perm.id,
                'codename': perm.codename,
                'name': perm.name,
                'content_type': category,
                'description': f"Can {perm.name.lower()}"
            }
            
            permissions_data.append(perm_data)
            
            if category not in permissions_by_category:
                permissions_by_category[category] = []
            permissions_by_category[category].append(perm_data)
        
        return Response({
            'permissions': permissions_data,
            'permissions_by_category': permissions_by_category,
            'total_permissions': len(permissions_data)
        })
    
    @action(detail=False, methods=['get'])
    def group_templates(self, request):
        """
        Get available group templates.
        """
        templates = GroupTemplate.objects.filter(is_system_template=True)
        serializer = GroupTemplateSerializer(templates, many=True)
        
        return Response({
            'templates': serializer.data,
            'total_templates': len(serializer.data)
        })
