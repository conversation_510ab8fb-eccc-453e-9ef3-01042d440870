"""
Management command to set up proper tenant-level groups and templates.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django_tenants.utils import schema_context, get_public_schema_name
from users.models import User
from users.models_groups import TenantGroup, GroupTemplate
from tenants.models import Tenant
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Set up proper tenant-level groups and templates'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 Setting up tenant-level groups and templates...'))
        self.stdout.write('='*60)
        
        try:
            # Step 1: Create required permissions
            self.create_required_permissions(options['dry_run'])
            
            # Step 2: Create group templates for each tenant level
            self.create_group_templates(options['dry_run'])
            
            # Step 3: Create actual groups from templates
            self.create_tenant_level_groups(options['dry_run'])
            
            # Step 4: Update existing SubCity Administrators group
            self.update_subcity_admin_group(options['dry_run'])
            
            self.stdout.write('')
            self.stdout.write('='*60)
            if options['dry_run']:
                self.stdout.write(self.style.WARNING('🔍 This was a dry run - no changes were made'))
            else:
                self.stdout.write(self.style.SUCCESS('✅ Tenant-level group setup completed!'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error during setup: {e}'))
            import traceback
            traceback.print_exc()

    def create_required_permissions(self, dry_run=False):
        """Create the required permissions if they don't exist."""
        self.stdout.write('🔧 Creating required permissions...')
        
        # Get User content type
        user_content_type = ContentType.objects.get_for_model(User)
        
        required_permissions = [
            # City level permissions
            ('manage_city_administration', 'Can manage city administration'),
            ('manage_subcities', 'Can manage subcities'),
            ('view_city_reports', 'Can view city reports'),
            ('manage_city_users', 'Can manage city users'),
            
            # Subcity level permissions
            ('manage_subcity_administration', 'Can manage subcity administration'),
            ('manage_kebeles', 'Can manage kebeles'),
            ('view_subcity_reports', 'Can view subcity reports'),
            ('manage_subcity_users', 'Can manage subcity users'),
            ('manage_citizens', 'Can manage citizens'),
            ('manage_idcards', 'Can manage ID cards'),
            ('view_citizens', 'Can view citizens'),
            ('view_idcards', 'Can view ID cards'),
            ('approve_id_cards', 'Can approve ID cards'),
            
            # Kebele level permissions
            ('manage_kebele_administration', 'Can manage kebele administration'),
            ('manage_kebele_users', 'Can manage kebele users'),
            ('create_citizens', 'Can create citizens'),
            ('create_idcards', 'Can create ID cards'),
            ('view_kebele_citizens', 'Can view kebele citizens'),
            ('view_kebele_idcards', 'Can view kebele ID cards'),
            
            # Printing permissions (flexible for autonomous kebeles)
            ('print_id_cards', 'Can print ID cards'),
            ('manage_printing_queue', 'Can manage printing queue'),
            
            # Navigation permissions
            ('navigate_to_dashboard', 'Can navigate to dashboard'),
            ('navigate_to_citizens', 'Can navigate to citizens'),
            ('navigate_to_id_cards', 'Can navigate to ID cards'),
            ('view_user_management', 'Can view user management'),
        ]
        
        created_count = 0
        for codename, name in required_permissions:
            if dry_run:
                exists = Permission.objects.filter(codename=codename, content_type=user_content_type).exists()
                if not exists:
                    self.stdout.write(f'  Would create permission: {codename}')
                    created_count += 1
                else:
                    self.stdout.write(f'  Permission already exists: {codename}')
            else:
                permission, created = Permission.objects.get_or_create(
                    codename=codename,
                    content_type=user_content_type,
                    defaults={'name': name}
                )
                if created:
                    self.stdout.write(f'  ✅ Created permission: {codename}')
                    created_count += 1
                else:
                    self.stdout.write(f'  ℹ️ Permission already exists: {codename}')
        
        self.stdout.write(f'📊 {"Would create" if dry_run else "Created"} {created_count} permissions')

    def create_group_templates(self, dry_run=False):
        """Create group templates for each tenant level."""
        self.stdout.write('🏗️ Creating group templates...')
        
        templates_config = [
            # City level templates
            {
                'name': 'City Administrator',
                'description': 'Full city administration permissions',
                'group_type': 'administrative',
                'level': 50,
                'tenant_types': ['city'],
                'permissions': [
                    'manage_city_administration', 'manage_subcities', 'view_city_reports',
                    'manage_city_users', 'navigate_to_dashboard', 'view_user_management'
                ]
            },
            
            # Subcity level templates
            {
                'name': 'Subcity Administrator',
                'description': 'Full subcity administration permissions',
                'group_type': 'administrative',
                'level': 40,
                'tenant_types': ['subcity'],
                'permissions': [
                    'manage_subcity_administration', 'manage_kebeles', 'view_subcity_reports',
                    'manage_subcity_users', 'manage_citizens', 'manage_idcards',
                    'view_citizens', 'view_idcards', 'approve_id_cards',
                    'navigate_to_dashboard', 'navigate_to_citizens', 'navigate_to_id_cards',
                    'view_user_management'
                ]
            },
            {
                'name': 'Subcity ID Card Manager',
                'description': 'ID card printing and management for subcity',
                'group_type': 'functional',
                'level': 35,
                'tenant_types': ['subcity'],
                'permissions': [
                    'print_id_cards', 'manage_printing_queue', 'view_idcards',
                    'navigate_to_id_cards'
                ]
            },
            
            # Kebele level templates
            {
                'name': 'Kebele Leader',
                'description': 'Kebele leadership and approval permissions',
                'group_type': 'administrative',
                'level': 30,
                'tenant_types': ['kebele'],
                'permissions': [
                    'manage_kebele_administration', 'manage_kebele_users',
                    'view_kebele_citizens', 'view_kebele_idcards',
                    'approve_id_cards', 'navigate_to_dashboard', 'view_user_management'
                ]
            },
            {
                'name': 'Kebele Clerk',
                'description': 'Citizen registration and ID card creation',
                'group_type': 'operational',
                'level': 20,
                'tenant_types': ['kebele'],
                'permissions': [
                    'create_citizens', 'create_idcards', 'view_kebele_citizens',
                    'view_kebele_idcards', 'navigate_to_dashboard',
                    'navigate_to_citizens', 'navigate_to_id_cards'
                ]
            },
            {
                'name': 'Autonomous Kebele Printer',
                'description': 'ID card printing for autonomous kebeles',
                'group_type': 'functional',
                'level': 25,
                'tenant_types': ['kebele'],
                'permissions': [
                    'print_id_cards', 'manage_printing_queue', 'view_kebele_idcards',
                    'navigate_to_id_cards'
                ]
            }
        ]
        
        created_count = 0
        with schema_context(get_public_schema_name()):
            for template_config in templates_config:
                if dry_run:
                    exists = GroupTemplate.objects.filter(name=template_config['name']).exists()
                    if not exists:
                        self.stdout.write(f'  Would create template: {template_config["name"]}')
                        created_count += 1
                    else:
                        self.stdout.write(f'  Template already exists: {template_config["name"]}')
                else:
                    template, created = GroupTemplate.objects.get_or_create(
                        name=template_config['name'],
                        defaults={
                            'description': template_config['description'],
                            'group_type': template_config['group_type'],
                            'level': template_config['level'],
                            'tenant_types': template_config['tenant_types'],
                            'is_system_template': True
                        }
                    )
                    
                    if created:
                        self.stdout.write(f'  ✅ Created template: {template_config["name"]}')
                        created_count += 1
                        
                        # Add permissions to template
                        for perm_codename in template_config['permissions']:
                            try:
                                permission = Permission.objects.get(codename=perm_codename)
                                template.permissions.add(permission)
                            except Permission.DoesNotExist:
                                self.stdout.write(f'    ⚠️ Permission not found: {perm_codename}')
                    else:
                        self.stdout.write(f'  ℹ️ Template already exists: {template_config["name"]}')
        
        self.stdout.write(f'📊 {"Would create" if dry_run else "Created"} {created_count} templates')

    def create_tenant_level_groups(self, dry_run=False):
        """Create actual groups from templates for existing tenants."""
        self.stdout.write('🏢 Creating tenant-level groups...')
        
        if dry_run:
            self.stdout.write('  Would create groups for existing tenants based on templates')
            return
        
        with schema_context(get_public_schema_name()):
            # Get all templates
            templates = GroupTemplate.objects.filter(is_system_template=True)
            
            for template in templates:
                for tenant_type in template.tenant_types:
                    # Get all tenants of this type
                    tenants = Tenant.objects.filter(type=tenant_type)
                    
                    for tenant in tenants:
                        group_name = f"{tenant.name}_{template.name.replace(' ', '_').lower()}"
                        
                        # Create Django group
                        django_group, created = Group.objects.get_or_create(name=group_name)
                        
                        if created:
                            # Add permissions from template
                            django_group.permissions.set(template.permissions.all())
                            self.stdout.write(f'  ✅ Created Django group: {group_name}')
                        
                        # Create TenantGroup
                        tenant_group, created = TenantGroup.objects.get_or_create(
                            group=django_group,
                            tenant=tenant,
                            defaults={
                                'name': template.name,
                                'description': f"{template.description} for {tenant.name}",
                                'group_type': template.group_type,
                                'level': template.level,
                                'allowed_tenant_types': [tenant_type],
                                'is_active': True
                            }
                        )
                        
                        if created:
                            self.stdout.write(f'  ✅ Created TenantGroup: {template.name} for {tenant.name}')

    def update_subcity_admin_group(self, dry_run=False):
        """Update the existing SubCity Administrators group to be subcity-specific."""
        self.stdout.write('🔄 Updating SubCity Administrators group...')
        
        if dry_run:
            self.stdout.write('  Would update SubCity Administrators group to be subcity-specific')
            return
        
        with schema_context(get_public_schema_name()):
            try:
                subcity_group = TenantGroup.objects.get(group__name='SubCity Administrators')
                
                # Update to be subcity-specific
                subcity_group.allowed_tenant_types = ['subcity']
                subcity_group.save()
                
                self.stdout.write('  ✅ Updated SubCity Administrators group to be subcity-specific')
                
            except TenantGroup.DoesNotExist:
                self.stdout.write('  ⚠️ SubCity Administrators group not found')
