from rest_framework import viewsets, permissions, status, generics
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework_simplejwt.views import TokenObtainPairView
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from .serializers import (
    UserSerializer, UserCreateSerializer, ChangePasswordSerializer,
    CustomTokenObtainPairSerializer
)
from .permissions import IsSelfOrAdmin
from common.permissions import CanManageUsers
from common.permissions_groups import CanManageUsersGroup


User = get_user_model()


class UserViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing users.

    Permissions:
    - Kebele admins can only manage clerk users in their kebele
    - Subcity admins can manage kebele admins and clerks in their subcity and child kebeles
    - City admins can manage subcity admins, kebele admins, and clerks in their city and child subcities/kebeles
    - Super admins have full access to all users
    """
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageUsersGroup]
    filterset_fields = ['role', 'tenant', 'is_active']

    def get_serializer_class(self):
        if self.action == 'create':
            return UserCreateSerializer
        return UserSerializer

    def get_queryset(self):
        """
        Filter users based on role and tenant hierarchy.

        - Clerks can only see themselves
        - Kebele admins can see clerks in their kebele
        - Subcity admins can see kebele admins and clerks in their subcity and child kebeles
        - City admins can see subcity admins, kebele admins, and clerks in their city and child subcities/kebeles
        - Super admins can see all users
        """
        user = self.request.user
        queryset = super().get_queryset()

        # Filter users based on role and tenant
        if user.is_superuser or user.role == 'superadmin':
            return queryset
        elif user.tenant:
            if user.role in ['city_admin', 'city_system_admin']:
                # City admin and city system admin can see all users in their city and sub-entities
                city = user.tenant
                subcities = city.children.all()
                subcity_ids = [subcity.id for subcity in subcities]
                kebeles = []
                for subcity in subcities:
                    kebeles.extend(subcity.children.all())
                kebele_ids = [kebele.id for kebele in kebeles]

                # City admins and city system admins can see subcity admins, kebele admins, and clerks
                return queryset.filter(
                    tenant__in=[city.id] + subcity_ids + kebele_ids,
                    role__in=['subcity_admin', 'subcity_system_admin', 'kebele_admin', 'kebele_leader', 'clerk']
                )
            elif user.role == 'subcity_admin':
                # Subcity admin can see all users in their subcity and kebeles
                subcity = user.tenant
                kebele_ids = [kebele.id for kebele in subcity.children.all()]

                # Subcity admins can see kebele admins and clerks
                return queryset.filter(
                    tenant__in=[subcity.id] + kebele_ids,
                    role__in=['kebele_admin', 'clerk']
                )
            elif user.role == 'kebele_admin':
                # Kebele admin can see clerks in their kebele
                return queryset.filter(
                    tenant=user.tenant,
                    role='clerk'
                ).union(queryset.filter(id=user.id))  # Include themselves

        # Clerks can only see themselves
        return queryset.filter(id=user.id)

    @action(detail=False, methods=['get'])
    def me(self, request):
        serializer = self.get_serializer(request.user)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated, IsSelfOrAdmin])
    def change_password(self, request, pk=None):
        user = self.get_object()
        serializer = ChangePasswordSerializer(data=request.data)

        if serializer.is_valid():
            # Check old password
            if not user.check_password(serializer.validated_data['old_password']):
                return Response({"old_password": [_("Wrong password.")]}, status=status.HTTP_400_BAD_REQUEST)

            # Set new password
            user.set_password(serializer.validated_data['new_password'])
            user.save()
            return Response({"message": _("Password updated successfully")}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer
