from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db import transaction
from django.utils import timezone
from django_tenants.utils import schema_context
from tenants.models import Tenant, TenantWorkflowConfig
from users.models import User
from rest_framework import serializers
from common.multi_tenant_rbac import MultiTenantRBAC


class WorkflowTransitionSerializer(serializers.Serializer):
    """Serializer for workflow transition requests."""
    workflow_type = serializers.ChoiceField(
        choices=['centralized', 'autonomous', 'hybrid', 'custom'],
        help_text="Target workflow type"
    )
    reason = serializers.CharField(
        max_length=500,
        help_text="Reason for workflow change"
    )
    effective_date = serializers.DateTimeField(
        required=False,
        help_text="When the workflow change should take effect (optional)"
    )


class WorkflowManagementViewSet(viewsets.ViewSet):
    """
    ViewSet for managing tenant workflow configurations.
    Allows switching between centralized, autonomous, and hybrid workflows.
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_tenant(self):
        """Get tenant from URL parameter."""
        tenant_id = self.kwargs.get('tenant_id')
        if not tenant_id:
            raise ValueError("Tenant ID is required")
        return Tenant.objects.get(id=tenant_id)

    def has_permission(self, request, view):
        """Check if user has permission to manage workflows."""
        user = request.user

        # Superusers have full permission
        if user.is_superuser:
            print(f"✅ Workflow management allowed: Superuser {user.email}")
            return True

        # Only subcity admins and city admins can change workflows
        if user.role not in ['city_admin', 'subcity_admin']:
            print(f"❌ Workflow management denied: Role {user.role} not authorized")
            return False

        # Check tenant hierarchy for permission
        try:
            tenant = self.get_tenant()
            user_tenant = getattr(user, 'tenant', None)

            if not user_tenant:
                print(f"❌ Workflow management denied: User {user.email} has no tenant")
                return False

            # Subcity admin can manage kebele workflows in their subcity
            if (user.role == 'subcity_admin' and
                user_tenant.type == 'subcity' and
                tenant.type == 'kebele' and
                tenant.parent_id == user_tenant.id):
                print(f"✅ Workflow management allowed: Subcity admin {user.email} managing child kebele {tenant.name}")
                return True

            # City admin can manage subcity workflows in their city
            if (user.role == 'city_admin' and
                user_tenant.type == 'city' and
                tenant.type == 'subcity' and
                tenant.parent_id == user_tenant.id):
                print(f"✅ Workflow management allowed: City admin {user.email} managing child subcity {tenant.name}")
                return True

            # Also use Multi-Tenant RBAC as fallback
            can_manage = MultiTenantRBAC.can_manage_workflow(user, tenant)
            print(f"🔍 MT-RBAC fallback: {user.email} can manage workflow for {tenant.name}: {can_manage}")
            return can_manage

        except Exception as e:
            print(f"❌ Error checking workflow management permission: {e}")
            return False

    def _is_child_kebele(self, subcity_tenant, kebele_tenant):
        """Check if kebele is under subcity's jurisdiction."""
        try:
            # Check tenant hierarchy - kebele should belong to the subcity
            from tenants.models.kebele import Kebele
            from tenants.models.subcity import SubCity

            # Get the subcity and kebele models
            subcity = SubCity.objects.filter(tenant=subcity_tenant).first()
            kebele = Kebele.objects.filter(tenant=kebele_tenant).first()

            if subcity and kebele:
                # Check if kebele belongs to this subcity (note: field is sub_city, not subcity)
                is_child = kebele.sub_city.id == subcity.id
                print(f"🔍 Hierarchy check: Kebele {kebele.name} belongs to Subcity {subcity.name}: {is_child}")
                return is_child
            else:
                print(f"⚠️ Could not find subcity or kebele models")
                return False

        except Exception as e:
            print(f"Error checking kebele hierarchy: {e}")
            # Fallback: check if kebele name contains subcity name (simple heuristic)
            try:
                return subcity_tenant.name.lower() in kebele_tenant.name.lower()
            except:
                return False

    @action(detail=False, methods=['get'])
    def current_workflow(self, request, tenant_id=None):
        """
        Get current workflow configuration for a tenant.
        """
        if not self.has_permission(request, self):
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        tenant = self.get_tenant()
        
        # Get or create workflow configuration
        workflow_config, created = TenantWorkflowConfig.objects.get_or_create(
            tenant=tenant,
            defaults={'workflow_type': 'centralized'}
        )
        
        return Response({
            'tenant': {
                'id': tenant.id,
                'name': tenant.name,
                'type': tenant.type
            },
            'workflow_type': workflow_config.workflow_type,
            'configuration': {
                'id_card_processing': workflow_config.id_card_processing,
                'citizen_registration': workflow_config.citizen_registration,
                'approval_workflow': workflow_config.approval_workflow,
            },
            'created_at': workflow_config.created_at,
            'updated_at': workflow_config.updated_at,
            'is_default': created
        })

    @action(detail=False, methods=['post'])
    def switch_workflow(self, request, tenant_id=None):
        """
        Switch tenant to a different workflow type.
        """
        if not self.has_permission(request, self):
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = WorkflowTransitionSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        tenant = self.get_tenant()
        new_workflow_type = serializer.validated_data['workflow_type']
        reason = serializer.validated_data['reason']

        try:
            with transaction.atomic():
                # Get or create workflow configuration
                workflow_config, created = TenantWorkflowConfig.objects.get_or_create(
                    tenant=tenant,
                    defaults={'workflow_type': 'centralized'}
                )

                old_workflow_type = workflow_config.workflow_type

                # Prevent unnecessary changes
                if old_workflow_type == new_workflow_type:
                    return Response({
                        'message': f'Tenant is already using {new_workflow_type} workflow',
                        'workflow_type': new_workflow_type
                    })

                # Apply new workflow configuration
                workflow_config.workflow_type = new_workflow_type
                
                if new_workflow_type == 'autonomous':
                    workflow_config.id_card_processing = self._get_autonomous_config()
                elif new_workflow_type == 'centralized':
                    workflow_config.id_card_processing = self._get_centralized_config()
                elif new_workflow_type == 'hybrid':
                    workflow_config.id_card_processing = self._get_hybrid_config()

                workflow_config.save()

                # Log the workflow change
                self._log_workflow_change(
                    tenant, old_workflow_type, new_workflow_type, 
                    request.user, reason
                )

                return Response({
                    'message': f'Workflow successfully changed from {old_workflow_type} to {new_workflow_type}',
                    'tenant': {
                        'id': tenant.id,
                        'name': tenant.name,
                        'type': tenant.type
                    },
                    'old_workflow': old_workflow_type,
                    'new_workflow': new_workflow_type,
                    'changed_by': request.user.email,
                    'reason': reason,
                    'configuration': workflow_config.id_card_processing
                })

        except Exception as e:
            return Response(
                {'error': f'Failed to switch workflow: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def available_workflows(self, request, tenant_id=None):
        """
        Get available workflow types for a tenant.
        """
        if not self.has_permission(request, self):
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        tenant = self.get_tenant()
        
        # Define available workflows based on tenant type
        available_workflows = []
        
        if tenant.type == 'kebele':
            available_workflows = [
                {
                    'type': 'centralized',
                    'name': 'Centralized Workflow',
                    'description': 'Standard hierarchy with subcity approval and printing',
                    'suitable_for': 'Traditional government structure'
                },
                {
                    'type': 'autonomous',
                    'name': 'Autonomous Workflow',
                    'description': 'Complete local control with kebele-level approval and printing',
                    'suitable_for': 'Self-sufficient kebeles with trained staff'
                },
                {
                    'type': 'hybrid',
                    'name': 'Hybrid Workflow',
                    'description': 'Local printing with hierarchical oversight',
                    'suitable_for': 'Kebeles transitioning to autonomy'
                }
            ]
        else:
            # Subcity and city tenants typically use centralized
            available_workflows = [
                {
                    'type': 'centralized',
                    'name': 'Centralized Workflow',
                    'description': 'Standard administrative workflow',
                    'suitable_for': 'Administrative oversight'
                }
            ]

        return Response({
            'tenant': {
                'id': tenant.id,
                'name': tenant.name,
                'type': tenant.type
            },
            'available_workflows': available_workflows
        })

    @action(detail=False, methods=['get'])
    def manage_child_kebeles(self, request):
        """
        Get all child kebeles that this subcity admin can manage.
        Only available for subcity admins.
        """
        user = request.user

        # Only subcity admins can use this endpoint
        if user.role != 'subcity_admin':
            return Response(
                {'error': 'Only subcity admins can manage child kebeles'},
                status=status.HTTP_403_FORBIDDEN
            )

        if not user.tenant or user.tenant.type != 'subcity':
            return Response(
                {'error': 'User must belong to a subcity tenant'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Get all kebeles under this subcity
            from tenants.models.kebele import Kebele
            from tenants.models.subcity import SubCity

            subcity = SubCity.objects.filter(tenant=user.tenant).first()
            if not subcity:
                return Response(
                    {'error': 'Subcity not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Get all kebeles under this subcity
            kebeles = Kebele.objects.filter(subcity=subcity)

            kebele_workflows = []
            for kebele in kebeles:
                # Get workflow configuration for each kebele
                workflow_config, created = TenantWorkflowConfig.objects.get_or_create(
                    tenant=kebele.tenant,
                    defaults={'workflow_type': 'centralized'}
                )

                # Get basic statistics
                kebele_stats = self._get_kebele_statistics(kebele.tenant)

                kebele_workflows.append({
                    'kebele': {
                        'id': kebele.tenant.id,
                        'name': kebele.name,
                        'tenant_name': kebele.tenant.name,
                        'schema_name': kebele.tenant.schema_name
                    },
                    'workflow': {
                        'type': workflow_config.workflow_type,
                        'can_print_locally': workflow_config.id_card_processing.get('can_print_locally', False),
                        'printing_authority': workflow_config.id_card_processing.get('printing_authority', 'subcity'),
                        'last_updated': workflow_config.updated_at
                    },
                    'statistics': kebele_stats,
                    'can_switch_to_autonomous': self._can_switch_to_autonomous(kebele.tenant, kebele_stats)
                })

            return Response({
                'subcity': {
                    'id': user.tenant.id,
                    'name': subcity.name,
                    'admin': user.email
                },
                'total_kebeles': len(kebeles),
                'kebeles': kebele_workflows,
                'workflow_summary': self._get_workflow_summary(kebele_workflows)
            })

        except Exception as e:
            return Response(
                {'error': f'Failed to get child kebeles: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _get_kebele_statistics(self, kebele_tenant):
        """Get basic statistics for a kebele."""
        try:
            with schema_context(kebele_tenant.schema_name):
                from idcards.models import IDCard, IDCardStatus
                from citizens.models import Citizen

                stats = {
                    'total_citizens': Citizen.objects.filter(is_active=True).count(),
                    'total_id_cards': IDCard.objects.count(),
                    'pending_approvals': IDCard.objects.filter(
                        status__in=[IDCardStatus.PENDING_APPROVAL, IDCardStatus.KEBELE_APPROVED]
                    ).count(),
                    'ready_to_print': IDCard.objects.filter(
                        status=IDCardStatus.APPROVED
                    ).count(),
                    'printed_cards': IDCard.objects.filter(
                        status=IDCardStatus.PRINTED
                    ).count()
                }

                return stats

        except Exception as e:
            print(f"Error getting kebele statistics: {e}")
            return {
                'total_citizens': 0,
                'total_id_cards': 0,
                'pending_approvals': 0,
                'ready_to_print': 0,
                'printed_cards': 0,
                'error': str(e)
            }

    def _can_switch_to_autonomous(self, kebele_tenant, stats):
        """Determine if kebele is ready for autonomous workflow."""
        # Simple criteria for autonomous readiness
        criteria = {
            'has_citizens': stats['total_citizens'] > 0,
            'has_activity': stats['total_id_cards'] > 0,
            'low_pending': stats['pending_approvals'] < 50,  # Not overwhelmed
            'staff_ready': True  # This would need to be tracked separately
        }

        is_ready = all(criteria.values())

        return {
            'ready': is_ready,
            'criteria': criteria,
            'recommendation': 'Ready for autonomous workflow' if is_ready else 'Needs preparation for autonomous workflow'
        }

    def _get_workflow_summary(self, kebele_workflows):
        """Get summary of workflow types across kebeles."""
        summary = {
            'centralized': 0,
            'autonomous': 0,
            'hybrid': 0,
            'total_pending_approvals': 0,
            'total_ready_to_print': 0
        }

        for kebele in kebele_workflows:
            workflow_type = kebele['workflow']['type']
            summary[workflow_type] = summary.get(workflow_type, 0) + 1
            summary['total_pending_approvals'] += kebele['statistics']['pending_approvals']
            summary['total_ready_to_print'] += kebele['statistics']['ready_to_print']

        return summary

    def _get_autonomous_config(self):
        """Get autonomous workflow configuration."""
        return {
            'can_print_locally': True,
            'requires_higher_approval': False,
            'approval_levels': ['kebele_leader'],
            'printing_authority': 'kebele',
            'quality_control': 'local',
            'granular_print_permissions': True,
            'print_roles': ['designated_printer', 'kebele_leader']
        }

    def _get_centralized_config(self):
        """Get centralized workflow configuration."""
        return {
            'can_print_locally': False,
            'requires_higher_approval': True,
            'approval_levels': ['kebele_leader', 'subcity_admin'],
            'printing_authority': 'subcity',
            'quality_control': 'centralized'
        }

    def _get_hybrid_config(self):
        """Get hybrid workflow configuration."""
        return {
            'can_print_locally': True,
            'requires_higher_approval': True,
            'approval_levels': ['kebele_leader'],
            'printing_authority': 'kebele',
            'quality_control': 'hybrid'
        }

    def _log_workflow_change(self, tenant, old_type, new_type, user, reason):
        """Log workflow changes for audit purposes."""
        try:
            # This could be enhanced to use a proper audit log model
            print(f"🔄 WORKFLOW CHANGE LOG:")
            print(f"   Tenant: {tenant.name} ({tenant.type})")
            print(f"   Changed by: {user.email} ({user.role})")
            print(f"   From: {old_type} → To: {new_type}")
            print(f"   Reason: {reason}")
            print(f"   Timestamp: {timezone.now()}")
        except Exception as e:
            print(f"Error logging workflow change: {e}")

    @action(detail=False, methods=['get'])
    def workflow_impact(self, request, tenant_id=None):
        """
        Analyze the impact of switching to a different workflow.
        """
        if not self.has_permission(request, self):
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        tenant = self.get_tenant()
        target_workflow = request.query_params.get('target_workflow', 'autonomous')

        # Analyze current state
        with schema_context(tenant.schema_name):
            from idcards.models import IDCard, IDCardStatus
            
            pending_cards = IDCard.objects.filter(
                status__in=[IDCardStatus.PENDING_APPROVAL, IDCardStatus.KEBELE_APPROVED]
            ).count()
            
            approved_cards = IDCard.objects.filter(
                status=IDCardStatus.APPROVED
            ).count()

        impact_analysis = {
            'tenant': {
                'id': tenant.id,
                'name': tenant.name,
                'type': tenant.type
            },
            'target_workflow': target_workflow,
            'current_state': {
                'pending_approvals': pending_cards,
                'ready_to_print': approved_cards
            },
            'impact': self._calculate_workflow_impact(target_workflow, pending_cards, approved_cards),
            'recommendations': self._get_workflow_recommendations(target_workflow, tenant)
        }

        return Response(impact_analysis)

    def _calculate_workflow_impact(self, target_workflow, pending_cards, approved_cards):
        """Calculate the impact of workflow change."""
        if target_workflow == 'autonomous':
            return {
                'printing_location': 'Local kebele office',
                'approval_time': 'Reduced by 50-70%',
                'pending_cards_effect': f'{pending_cards} cards can be processed locally',
                'staff_training_required': True,
                'equipment_needed': 'ID card printer at kebele level'
            }
        elif target_workflow == 'centralized':
            return {
                'printing_location': 'Subcity office',
                'approval_time': 'Standard processing time',
                'pending_cards_effect': f'{pending_cards} cards follow standard approval',
                'staff_training_required': False,
                'equipment_needed': 'None (existing infrastructure)'
            }
        else:
            return {
                'printing_location': 'Local with oversight',
                'approval_time': 'Moderately reduced',
                'pending_cards_effect': f'{pending_cards} cards processed with local printing',
                'staff_training_required': True,
                'equipment_needed': 'ID card printer and oversight system'
            }

    def _get_workflow_recommendations(self, target_workflow, tenant):
        """Get recommendations for workflow transition."""
        recommendations = []
        
        if target_workflow == 'autonomous':
            recommendations = [
                'Ensure kebele staff are trained on ID card approval process',
                'Install and test ID card printing equipment',
                'Establish local quality control procedures',
                'Set up designated printer user accounts',
                'Create backup procedures for equipment failures'
            ]
        elif target_workflow == 'centralized':
            recommendations = [
                'Coordinate with subcity office for processing capacity',
                'Establish clear communication channels',
                'Train staff on submission procedures'
            ]
        
        return recommendations
